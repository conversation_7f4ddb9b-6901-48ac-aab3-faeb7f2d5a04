
import { Button } from "@/components/ui/button";

interface GalleryFiltersProps {
  activeFilter: string;
  setActiveFilter: (filter: string) => void;
}

const GalleryFilters = ({ activeFilter, setActiveFilter }: GalleryFiltersProps) => {
  const categories = [
    { value: "all", label: "All Photos" },
    { value: "classroom", label: "Classroom" },
    { value: "outdoor", label: "Outdoor" },
    { value: "activities", label: "Activities" },
    { value: "events", label: "Events" }
  ];

  return (
    <div className="flex flex-wrap justify-center gap-3 mb-8">
      {categories.map(category => (
        <Button
          key={category.value}
          variant={activeFilter === category.value ? "default" : "outline"}
          onClick={() => setActiveFilter(category.value)}
          className={`
            px-5 py-2 transition-all duration-300
            ${activeFilter === category.value 
              ? "bg-montessori-terracotta hover:bg-montessori-terracotta/90 text-white" 
              : "border-montessori-sage text-montessori-text hover:bg-montessori-sage/10"}
          `}
        >
          {category.label}
        </Button>
      ))}
    </div>
  );
};

export default GalleryFilters;
