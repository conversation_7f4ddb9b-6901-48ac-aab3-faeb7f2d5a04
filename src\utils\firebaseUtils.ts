import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  where,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage';
import { db, storage } from '@/lib/firebase';
import { 
  ContactMessage, 
  EnrollmentForm, 
  TourSchedule, 
  GalleryPhoto, 
  Announcement,
  BlogPost 
} from '@/types/firebase';

// Gallery functions
export const uploadGalleryPhoto = async (file: File, title: string, description: string, category: string) => {
  try {
    // Upload the file first
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    const filePath = `gallery/${fileName}`;
    const storageRef = ref(storage, filePath);

    const uploadResult = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(uploadResult.ref);

    // Create the gallery entry in Firestore
    const docRef = await addDoc(collection(db, 'gallery_photos'), {
      title,
      description,
      category,
      image_url: downloadURL,
      created_at: serverTimestamp()
    });

    return downloadURL;
  } catch (error: any) {
    console.error("Gallery upload error:", error);
    throw new Error(`Failed to upload photo: ${error.message}`);
  }
};

export const getGalleryPhotos = async (): Promise<GalleryPhoto[]> => {
  try {
    const q = query(collection(db, 'gallery_photos'), orderBy('created_at', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as GalleryPhoto));
  } catch (error: any) {
    console.error("Error fetching gallery photos:", error);
    throw error;
  }
};

export const deleteGalleryPhoto = async (id: string, imageUrl: string) => {
  try {
    // Delete from Firestore
    await deleteDoc(doc(db, 'gallery_photos', id));
    
    // Delete from Storage
    const imageRef = ref(storage, imageUrl);
    await deleteObject(imageRef);
  } catch (error: any) {
    console.error("Error deleting gallery photo:", error);
    throw error;
  }
};

export const updateGalleryPhoto = async (id: string, updates: Partial<GalleryPhoto>) => {
  try {
    const docRef = doc(db, 'gallery_photos', id);
    await updateDoc(docRef, updates);
  } catch (error: any) {
    console.error("Error updating gallery photo:", error);
    throw error;
  }
};

// Contact message functions
export const submitContactMessage = async (message: Omit<ContactMessage, 'id' | 'read' | 'created_at'>) => {
  try {
    await addDoc(collection(db, 'contact_messages'), {
      ...message,
      read: false,
      created_at: serverTimestamp()
    });
  } catch (error: any) {
    console.error("Error submitting contact message:", error);
    throw error;
  }
};

export const getContactMessages = async (): Promise<ContactMessage[]> => {
  try {
    const q = query(collection(db, 'contact_messages'), orderBy('created_at', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ContactMessage));
  } catch (error: any) {
    console.error("Error fetching contact messages:", error);
    throw error;
  }
};

export const markMessageAsRead = async (id: string) => {
  try {
    const docRef = doc(db, 'contact_messages', id);
    await updateDoc(docRef, { read: true });
  } catch (error: any) {
    console.error("Error marking message as read:", error);
    throw error;
  }
};

export const deleteContactMessage = async (id: string) => {
  try {
    await deleteDoc(doc(db, 'contact_messages', id));
  } catch (error: any) {
    console.error("Error deleting contact message:", error);
    throw error;
  }
};

// Tour schedule functions
export const submitTourSchedule = async (schedule: Omit<TourSchedule, 'id' | 'status' | 'created_at'>) => {
  try {
    await addDoc(collection(db, 'tour_schedules'), {
      ...schedule,
      status: 'pending' as const,
      created_at: serverTimestamp()
    });
  } catch (error: any) {
    console.error("Error submitting tour schedule:", error);
    throw error;
  }
};

export const getTourSchedules = async (): Promise<TourSchedule[]> => {
  try {
    const q = query(collection(db, 'tour_schedules'), orderBy('created_at', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as TourSchedule));
  } catch (error: any) {
    console.error("Error fetching tour schedules:", error);
    throw error;
  }
};

export const updateTourScheduleStatus = async (id: string, status: TourSchedule['status']) => {
  try {
    const docRef = doc(db, 'tour_schedules', id);
    await updateDoc(docRef, { status });
  } catch (error: any) {
    console.error("Error updating tour schedule status:", error);
    throw error;
  }
};

export const deleteTourSchedule = async (id: string) => {
  try {
    await deleteDoc(doc(db, 'tour_schedules', id));
  } catch (error: any) {
    console.error("Error deleting tour schedule:", error);
    throw error;
  }
};

// Enrollment form functions
export const submitEnrollmentForm = async (form: Omit<EnrollmentForm, 'id' | 'status' | 'created_at'>) => {
  try {
    await addDoc(collection(db, 'enrollment_forms'), {
      ...form,
      status: 'pending' as const,
      created_at: serverTimestamp()
    });
  } catch (error: any) {
    console.error("Error submitting enrollment form:", error);
    throw error;
  }
};

export const getEnrollmentForms = async (): Promise<EnrollmentForm[]> => {
  try {
    const q = query(collection(db, 'enrollment_forms'), orderBy('created_at', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as EnrollmentForm));
  } catch (error: any) {
    console.error("Error fetching enrollment forms:", error);
    throw error;
  }
};

export const updateEnrollmentFormStatus = async (id: string, status: EnrollmentForm['status']) => {
  try {
    const docRef = doc(db, 'enrollment_forms', id);
    await updateDoc(docRef, { status });
  } catch (error: any) {
    console.error("Error updating enrollment form status:", error);
    throw error;
  }
};

export const deleteEnrollmentForm = async (id: string) => {
  try {
    await deleteDoc(doc(db, 'enrollment_forms', id));
  } catch (error: any) {
    console.error("Error deleting enrollment form:", error);
    throw error;
  }
};

// Announcement functions
export const getAnnouncements = async (): Promise<Announcement[]> => {
  try {
    const q = query(collection(db, 'announcements'), orderBy('created_at', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Announcement));
  } catch (error: any) {
    console.error("Error fetching announcements:", error);
    throw error;
  }
};

export const createAnnouncement = async (announcement: Omit<Announcement, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    await addDoc(collection(db, 'announcements'), {
      ...announcement,
      created_at: serverTimestamp(),
      updated_at: serverTimestamp()
    });
  } catch (error: any) {
    console.error("Error creating announcement:", error);
    throw error;
  }
};

export const updateAnnouncement = async (id: string, updates: Partial<Announcement>) => {
  try {
    const docRef = doc(db, 'announcements', id);
    await updateDoc(docRef, {
      ...updates,
      updated_at: serverTimestamp()
    });
  } catch (error: any) {
    console.error("Error updating announcement:", error);
    throw error;
  }
};

export const deleteAnnouncement = async (id: string) => {
  try {
    await deleteDoc(doc(db, 'announcements', id));
  } catch (error: any) {
    console.error("Error deleting announcement:", error);
    throw error;
  }
};
