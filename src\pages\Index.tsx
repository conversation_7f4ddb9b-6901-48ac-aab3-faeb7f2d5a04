import MainLayout from "@/components/layout/MainLayout";
import { motion, useScroll, useTransform } from "framer-motion";
import ScrollReveal from "@/components/animations/ScrollReveal";
import AnimatedElement from "@/components/animations/AnimatedElement";
import NurseryScene from "@/components/3d/NurseryScene";
import { Button } from "@/components/ui/button";
import { ArrowRight, BookOpen, Users, Heart, Calendar, MapPin, Star, ArrowDown, Compass, Sparkles, MousePointerClick, GraduationCap } from "lucide-react";
import { useRef } from "react";
import TourDialog from "@/components/dialogs/TourDialog";
import EnrollmentDialog from "@/components/dialogs/EnrollmentDialog";

const Index = () => {
  const targetRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: targetRef,
    offset: ["start end", "end start"]
  });
  
  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0]);
  const scale = useTransform(scrollYProgress, [0, 0.5], [1, 0.8]);
  const y = useTransform(scrollYProgress, [0, 0.5], [0, -50]);

  return (
    <MainLayout>
      {/* Hero Section */}
      <section ref={targetRef} className="relative min-h-screen flex items-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-montessori-beige/80 to-white z-0" />
        <div className="container mx-auto px-4 z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <h1 className="font-serif text-4xl md:text-5xl lg:text-7xl font-bold text-montessori-text leading-tight mb-6">
                Nurturing Young Minds Through
                <span className="text-montessori-terracotta"> Play & Learning</span>
              </h1>
              <p className="text-lg md:text-xl text-montessori-text/80 mb-8 max-w-lg">
                At Given Promise Academy, we provide a safe, nurturing environment where children aged 2-6 years
                develop essential skills through play-based learning and structured activities.
              </p>
              <div className="flex flex-wrap gap-4">
                <TourDialog />
                <EnrollmentDialog />
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.3 }}
              className="relative"
              style={{ opacity, scale, y }}
            >
              <NurseryScene />
            </motion.div>
          </div>
        </div>
        <motion.div 
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
        >
          <ArrowDown className="h-8 w-8 text-montessori-terracotta" />
        </motion.div>
      </section>

      {/* Philosophy Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="font-serif text-3xl md:text-4xl font-bold text-center text-montessori-text mb-16">
              Our <span className="text-montessori-terracotta">Early Learning</span> Approach
            </h2>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-16">
            {[
              {
                icon: BookOpen,
                title: "Play-Based Learning",
                desc: "Children learn through structured play activities that develop cognitive and social skills naturally."
              },
              {
                icon: Users,
                title: "Small Class Sizes",
                desc: "Low teacher-to-child ratios ensure personalized attention and care for each child."
              },
              {
                icon: Heart,
                title: "Nurturing Environment",
                desc: "We create a warm, safe space where children feel loved and confident to explore."
              },
              {
                icon: Calendar,
                title: "Structured Routine",
                desc: "Daily schedules provide security while allowing flexibility for individual needs."
              }
            ].map((item, index) => (
              <ScrollReveal key={index} delay={index * 0.1} direction={index % 2 === 0 ? "left" : "right"}>
                <div className="bg-montessori-beige/30 p-6 rounded-lg h-full flex flex-col items-center text-center hover:shadow-md transition-shadow duration-300">
                  <div className="bg-montessori-beige w-16 h-16 rounded-full flex items-center justify-center mb-4">
                    <item.icon className="h-8 w-8 text-montessori-terracotta" />
                  </div>
                  <h3 className="font-serif text-xl font-semibold mb-3 text-montessori-text">{item.title}</h3>
                  <p className="text-montessori-text/80">{item.desc}</p>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-montessori-beige/20">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="font-serif text-3xl md:text-4xl font-bold text-center text-montessori-text mb-6">
              Why Choose <span className="text-montessori-terracotta">Given Promise Academy</span>
            </h2>
            <p className="text-lg text-center text-montessori-text/80 max-w-3xl mx-auto mb-16">
              Our nursery and preschool programs provide the perfect foundation for your child's educational journey.
            </p>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <ScrollReveal delay={0.1} direction="up">
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col h-full">
                <div className="bg-montessori-blue/20 w-14 h-14 rounded-full flex items-center justify-center mb-5">
                  <Compass className="h-7 w-7 text-montessori-blue" />
                </div>
                <h3 className="font-serif text-xl font-semibold mb-3 text-montessori-text">Early Development</h3>
                <p className="text-montessori-text/80 mb-4 flex-grow">
                  We focus on developing essential skills like language, motor skills, and social interaction through age-appropriate activities.
                </p>
              
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.3} direction="up">
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col h-full">
                <div className="bg-montessori-terracotta/20 w-14 h-14 rounded-full flex items-center justify-center mb-5">
                  <Sparkles className="h-7 w-7 text-montessori-terracotta" />
                </div>
                <h3 className="font-serif text-xl font-semibold mb-3 text-montessori-text">Creative Play</h3>
                <p className="text-montessori-text/80 mb-4 flex-grow">
                  Through art, music, storytelling, and imaginative play, children develop creativity and self-expression in a fun environment.
                </p>
              
              </div>
            </ScrollReveal>

            <ScrollReveal delay={0.5} direction="up">
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col h-full">
                <div className="bg-montessori-sage/30 w-14 h-14 rounded-full flex items-center justify-center mb-5">
                  <GraduationCap className="h-7 w-7 text-montessori-sage" />
                </div>
                <h3 className="font-serif text-xl font-semibold mb-3 text-montessori-text">School Readiness</h3>
                <p className="text-montessori-text/80 mb-4 flex-grow">
                  We prepare children for primary school with pre-literacy, numeracy, and social skills that ensure a smooth transition.
                </p>
               
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="font-serif text-3xl md:text-4xl font-bold text-center text-montessori-text mb-6">
              What Parents <span className="text-montessori-terracotta">Say</span>
            </h2>
            <p className="text-lg text-center text-montessori-text/80 max-w-3xl mx-auto mb-16">
              Hear from our community of parents about their experiences with our nursery and preschool programs.
            </p>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((item, index) => (
              <ScrollReveal key={index} delay={index * 0.2}>
                <div className="bg-montessori-beige/20 p-8 rounded-xl relative">
                  <div className="absolute -top-4 left-8 text-montessori-terracotta">
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M11.6 20C7.2 20 4 23.2 4 27.6C4 32 7.2 35.2 11.6 35.2C16 35.2 19.2 32 19.2 27.6V12H11.6V20ZM25.6 20C21.2 20 18 23.2 18 27.6C18 32 21.2 35.2 25.6 35.2C30 35.2 33.2 32 33.2 27.6V12H25.6V20Z" fill="currentColor" fillOpacity="0.2"/>
                    </svg>
                  </div>
                  <div className="flex flex-row space-x-1 mb-4 pt-3">
                    {Array(5).fill(0).map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-montessori-terracotta text-montessori-terracotta" />
                    ))}
                  </div>
                  <p className="text-montessori-text/90 mb-6 italic">
                    "Our child has thrived in this nurturing environment. The teachers are attentive and caring, and the activities encourage independence while fostering a love for learning."
                  </p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-montessori-terracotta/20 rounded-full flex items-center justify-center mr-4">
                      <span className="text-montessori-terracotta font-bold">
                        {["S", "J", "K"][index]}
                      </span>
                    </div>
                    <div>
                      <p className="font-semibold text-montessori-text">
                        {["Sarah P.", "James T.", "Katherine M."][index]}
                      </p>
                      <p className="text-sm text-montessori-text/70">Parent</p>
                    </div>
                  </div>
                </div>
              </ScrollReveal>
            ))}
          </div>

         
        </div>
      </section>

      {/* Interactive Call-to-Action */}
      <section className="py-16 bg-montessori-blue/10 relative overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full opacity-20">
          <div className="w-40 h-40 rounded-full bg-montessori-terracotta/30 absolute -top-10 -left-10"></div>
          <div className="w-60 h-60 rounded-full bg-montessori-blue/30 absolute -bottom-20 -right-20"></div>
          <div className="w-32 h-32 rounded-full bg-montessori-sage/30 absolute top-20 right-40"></div>
        </div>
        
        <div className="container mx-auto px-4 relative z-10">
          <ScrollReveal>
            <div className="bg-white rounded-xl shadow-lg p-8 md:p-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                  <h2 className="font-serif text-3xl font-bold mb-6 text-montessori-text">Visit Our <span className="text-montessori-terracotta">Nursery School</span></h2>
                  <p className="text-lg text-montessori-text/80 mb-8">
                    Come see our warm, welcoming environment and discover how Given Promise Academy can be the perfect start for your child's educational journey.
                  </p>
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-start">
                      <MousePointerClick className="h-5 w-5 text-montessori-terracotta mr-3 mt-0.5" />
                      <span className="text-montessori-text/80">Schedule a convenient visit time</span>
                    </li>
                    <li className="flex items-start">
                      <Users className="h-5 w-5 text-montessori-terracotta mr-3 mt-0.5" />
                      <span className="text-montessori-text/80">Meet our caring and qualified teachers</span>
                    </li>
                    <li className="flex items-start">
                      <Compass className="h-5 w-5 text-montessori-terracotta mr-3 mt-0.5" />
                      <span className="text-montessori-text/80">See our safe and stimulating learning spaces</span>
                    </li>
                  </ul>
                  <div className="flex flex-wrap gap-4">
                    <TourDialog />
                    <EnrollmentDialog />
                  </div>
                </div>
                <div className="lg:pl-10">
                  <AnimatedElement type="scale">
                    <div className="aspect-square bg-montessori-beige/30 rounded-xl p-8 flex items-center justify-center">
                      <div className="text-center">
                        <div className="inline-block p-6 rounded-full bg-montessori-beige mb-6">
                          <Calendar className="h-12 w-12 text-montessori-terracotta" />
                        </div>
                        <h3 className="text-2xl font-serif font-bold text-montessori-text mb-4">Now Enrolling</h3>
                        <p className="text-montessori-text/80 mb-6">
                          Limited spaces available for the upcoming session
                        </p>
                        <div className="bg-white py-2 px-4 rounded-full inline-block text-sm text-montessori-terracotta font-medium">
                          Starts Today
                        </div>
                      </div>
                    </div>
                  </AnimatedElement>
                </div>
              </div>
            </div>
          </ScrollReveal>
        </div>
      </section>
    </MainLayout>
  );
};

export default Index;
