
import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";

const CookieSettings = () => {
  const [preferences, setPreferences] = useState({
    essential: true, // Always true and disabled
    analytics: false,
    marketing: false,
  });
  const { toast } = useToast();

  useEffect(() => {
    const savedPreferences = localStorage.getItem('cookiePreferences');
    if (savedPreferences) {
      setPreferences(JSON.parse(savedPreferences));
    }
  }, []);

  const handleSave = () => {
    localStorage.setItem('cookiePreferences', JSON.stringify(preferences));
    localStorage.setItem('cookieConsent', 'customized');
    toast({
      title: "Settings saved",
      description: "Your cookie preferences have been updated."
    });
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8 max-w-3xl">
        <h1 className="text-3xl font-serif font-semibold text-montessori-text mb-6 text-center">
          Cookie Settings
        </h1>
        <div className="bg-white rounded-lg shadow-md p-6 space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between py-3 border-b">
              <div>
                <h3 className="font-medium text-montessori-text">Essential Cookies</h3>
                <p className="text-sm text-muted-foreground">Required for the website to function properly</p>
              </div>
              <Switch checked disabled />
            </div>

            <div className="flex items-center justify-between py-3 border-b">
              <div>
                <h3 className="font-medium text-montessori-text">Analytics Cookies</h3>
                <p className="text-sm text-muted-foreground">Help us understand how visitors interact with the website</p>
              </div>
              <Switch
                checked={preferences.analytics}
                onCheckedChange={(checked) => setPreferences(prev => ({ ...prev, analytics: checked }))}
              />
            </div>

            <div className="flex items-center justify-between py-3 border-b">
              <div>
                <h3 className="font-medium text-montessori-text">Marketing Cookies</h3>
                <p className="text-sm text-muted-foreground">Used to deliver personalized advertisements</p>
              </div>
              <Switch
                checked={preferences.marketing}
                onCheckedChange={(checked) => setPreferences(prev => ({ ...prev, marketing: checked }))}
              />
            </div>
          </div>

          <div className="flex justify-center pt-4">
            <Button
              className="bg-montessori-terracotta hover:bg-montessori-terracotta/80"
              onClick={handleSave}
            >
              Save Preferences
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default CookieSettings;
