# CRUSH Configuration for Given Promise Academy

## Project Overview
- React + TypeScript + Vite project with shadcn/ui components
- Firebase integration for backend services
- Tailwind CSS for styling
- React Three Fiber for 3D components

## Essential Commands
- **Dev server**: `npm run dev`
- **Build**: `npm run build`
- **Lint**: `npm run lint`
- **Preview build**: `npm run preview`

## Code Style Guidelines
- Use TypeScript with explicit typing for props and state
- Component file names in PascalCase with .tsx extension
- Prefer functional components with TypeScript interfaces
- Use absolute imports with @/ prefix (e.g., @/components/ui/button)
- Tailwind classes preferred over custom CSS when possible
- Follow existing shadcn/ui component patterns for new UI components

## Testing
- No test framework currently configured in the project
- Add tests when needed using Jest or <PERSON>ites<PERSON> if required

## Naming Conventions
- Components: PascalCase (But<PERSON>, Card, Header)
- Files: PascalCase for components, camelCase for utilities
- Variables: camelCase
- Constants: UPPER_SNAKE_CASE

## Error Handling
- Use try/catch blocks for async operations
- Implement error boundaries for component-level error handling
- Handle Firebase errors with specific error codes when possible

## Additional Notes
- No Cursor or Copilot rules found in the project
- Ensure all new dependencies are compatible with existing stack
- Use existing utility functions in src/lib and src/utils when possible