import { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useQuery } from "@tanstack/react-query";
import { collection, query, where, orderBy, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { BlogPost } from "@/types/firebase";
import { Search } from "lucide-react";

const Blog = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const { data: posts, isLoading } = useQuery({
    queryKey: ["blog-posts"],
    queryFn: async () => {
      const q = query(
        collection(db, "blog_posts"),
        where("published", "==", true),
        orderBy("created_at", "desc")
      );
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BlogPost));
    }
  });

  const filteredPosts = posts?.filter(post =>
    post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
    post.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-serif font-bold text-montessori-text mb-8 text-center">
            Educational Blog
          </h1>
          
          <div className="relative mb-8">
            <Input
              type="search"
              placeholder="Search articles..."
              className="w-full pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>

          {isLoading ? (
            <div className="text-center py-10">Loading articles...</div>
          ) : !filteredPosts?.length ? (
            <div className="text-center py-10">No articles found.</div>
          ) : (
            <div className="grid gap-6">
              {filteredPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-6">
                      {post.image_url && (
                        <img
                          src={post.image_url}
                          alt={post.title}
                          className="w-full md:w-48 h-48 object-cover rounded-md"
                        />
                      )}
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-montessori-sage font-medium">
                            {post.category}
                          </span>
                          <span className="text-sm text-gray-500">
                            {post.created_at.toDate().toLocaleDateString()}
                          </span>
                        </div>
                        <h2 className="text-xl font-semibold text-montessori-text mb-2">
                          <a href={`/blog/${post.slug}`} className="hover:text-montessori-terracotta">
                            {post.title}
                          </a>
                        </h2>
                        <p className="text-gray-600 mb-4 line-clamp-2">
                          {post.excerpt}
                        </p>
                        <a
                          href={`/blog/${post.slug}`}
                          className="text-montessori-terracotta hover:text-montessori-terracotta/80 font-medium inline-flex items-center"
                        >
                          Read More
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default Blog;