
import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import EnrollmentDialog from "../dialogs/EnrollmentDialog";

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const navItems = [
    { name: "Home", path: "/" },
    { name: "About", path: "/about" },
    { name: "Programs", path: "/programs" },
    { name: "Gallery", path: "/gallery" },
    { name: "Contact", path: "/contact" },
  ];

  return (
    <header
      className={`fixed w-full z-50 ${
        isScrolled
          ? "bg-white bg-opacity-95 shadow-md py-2"
          : "bg-transparent py-4"
      }`}
    >
      <div className="container mx-auto px-4 flex items-center justify-between w-full">
        <Link to="/" className="flex items-center" onClick={scrollToTop}>
          <div className="flex items-center">
            <img
              src="/images/logo.png"
              alt="Given Promise Academy"
              className="h-8 sm:h-10 w-auto mr-2"
            />
            <span className="text-lg sm:text-xl font-serif font-bold text-montessori-text whitespace-nowrap">
              Given Promise
              <span className="text-montessori-navy"> Academy</span>
            </span>
          </div>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-1">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.path}
              onClick={scrollToTop}
              className="relative px-3 py-2 font-medium text-montessori-text hover:text-montessori-navy"
            >
              {item.name}
            </Link>
          ))}
          <EnrollmentDialog>
            <Button className="ml-4 bg-montessori-navy hover:bg-montessori-navy/80 text-white">Enroll Now</Button>
          </EnrollmentDialog>
        </nav>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="text-montessori-text"
            aria-label="Toggle menu"
          >
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div
          className="md:hidden bg-white shadow-lg py-4 absolute w-full"
        >
          <nav className="flex flex-col space-y-4 px-4">
            {navItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className="py-2 px-4 hover:bg-montessori-navy/10 rounded-md text-montessori-text hover:text-montessori-navy"
                onClick={() => {
                  setMobileMenuOpen(false);
                  scrollToTop();
                }}
              >
                {item.name}
              </Link>
            ))}
            <div className="px-4 pb-2">
              <EnrollmentDialog>
                <Button className="bg-montessori-navy hover:bg-montessori-navy/80 text-white w-full">
                  Enroll Now
                </Button>
              </EnrollmentDialog>
            </div>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
