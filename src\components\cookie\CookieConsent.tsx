
import { useEffect, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON> } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";

const CookieConsent = () => {
  const [showConsent, setShowConsent] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const consent = localStorage.getItem('cookieConsent');
    if (!consent) {
      setShowConsent(true);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('cookieConsent', 'accepted');
    setShowConsent(false);
    toast({
      title: "Cookies accepted",
      description: "Your preferences have been saved."
    });
  };

  const handleDecline = () => {
    localStorage.setItem('cookieConsent', 'declined');
    setShowConsent(false);
    toast({
      title: "Cookies declined",
      description: "Only essential cookies will be used."
    });
  };

  if (!showConsent) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-lg z-50">
      <div className="container mx-auto max-w-4xl flex flex-col md:flex-row items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <Cookie className="h-5 w-5 text-montessori-terracotta" />
          <p className="text-sm text-montessori-text text-center md:text-left">
            We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDecline}
          >
            Decline
          </Button>
          <Button
            className="bg-montessori-terracotta hover:bg-montessori-terracotta/80"
            size="sm"
            onClick={handleAccept}
          >
            Accept
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
