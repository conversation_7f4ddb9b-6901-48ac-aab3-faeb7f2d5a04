import MainLayout from "@/components/layout/MainLayout";
import { motion } from "framer-motion";
import ScrollReveal from "@/components/animations/ScrollReveal";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowRight, Clock, Calendar, Users, BookOpen } from "lucide-react";

const Programs = () => {
  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-to-b from-montessori-beige/40 to-white">
        <div className="container mx-auto px-4">
          <ScrollReveal direction="up" delay={0.2}>
            <h1 className="font-serif text-4xl md:text-5xl lg:text-6xl font-bold text-center text-montessori-text mb-6">
              Our <span className="text-montessori-terracotta">Early Learning</span> Programs
            </h1>
            <p className="text-montessori-text/80 text-lg md:text-xl text-center max-w-3xl mx-auto mb-12">
              Discover our age-appropriate programs designed to support your child's development
              through play-based learning and structured activities.
            </p>
          </ScrollReveal>
        </div>
      </section>

      {/* Programs Tab Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <Tabs defaultValue="infant" className="w-full max-w-5xl mx-auto">
              <div className="flex justify-center mb-8">
                <TabsList className="bg-montessori-beige/30">
                  <TabsTrigger value="infant" className="text-sm md:text-base">Nursery (2-3 years)</TabsTrigger>
                  <TabsTrigger value="toddler" className="text-sm md:text-base">Preschool (4-6 years)</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="infant" className="focus-visible:ring-0 focus-visible:ring-offset-0">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                  <div>
                    <h2 className="font-serif text-3xl font-bold text-montessori-text mb-4">
                      Nursery Program <span className="text-montessori-terracotta">(2-3 years)</span>
                    </h2>
                    <p className="text-montessori-text/80 mb-4">
                    Our nursery program provides a gentle introduction to school life for toddlers aged 2-3 years. Children develop basic social skills, language development, and motor skills through play-based activities in a safe, nurturing environment with qualified caregivers.
                    </p>
                    <div className="space-y-4 mb-6">
                      <div className="flex items-start gap-3">
                        <Clock className="h-6 w-6 text-montessori-terracotta flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-montessori-text">Hours</h3>
                          <p className="text-montessori-text/70">Monday to Friday: 7:30 AM - 4:00 PM</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <Users className="h-6 w-6 text-montessori-terracotta flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-montessori-text">Class Size</h3>
                          <p className="text-montessori-text/70">Maximum of 15 children with 2 qualified caregivers</p>
                        </div>
                      </div>
                    </div>
                    <Button className="bg-montessori-terracotta hover:bg-montessori-terracotta/90 text-white">
                      Enroll Now <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                  <div className="relative">
                    <div className="absolute -top-4 -left-4 w-24 h-24 bg-montessori-terracotta/20 rounded-full"></div>
                    <div className="aspect-[4/3] overflow-hidden rounded-lg shadow-lg relative z-10">
                      <img
                        src="https://images.unsplash.com/photo-1544717297-fa95b6ee9643?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                        alt="Nursery program children playing"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-montessori-sage/30 rounded-full"></div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="toddler" className="focus-visible:ring-0 focus-visible:ring-offset-0">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                  <div>
                    <h2 className="font-serif text-3xl font-bold text-montessori-text mb-4">
                      Preschool Program <span className="text-montessori-terracotta">(4-6 years)</span>
                    </h2>
                    <p className="text-montessori-text/80 mb-4">
                    Our preschool program prepares children aged 4-6 years for primary school through structured learning activities. Children develop pre-literacy and numeracy skills, enhance their social abilities, and build confidence through age-appropriate curriculum and experienced teachers.
                    </p>
                    <div className="space-y-4 mb-6">
                      <div className="flex items-start gap-3">
                        <Clock className="h-6 w-6 text-montessori-terracotta flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-montessori-text">Hours</h3>
                          <p className="text-montessori-text/70">Monday to Friday: 7:30 AM - 4:00 PM</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <Users className="h-6 w-6 text-montessori-terracotta flex-shrink-0 mt-1" />
                        <div>
                          <h3 className="font-semibold text-montessori-text">Class Size</h3>
                          <p className="text-montessori-text/70">Maximum of 20 children with 2 qualified teachers</p>
                        </div>
                      </div>
                    </div>
                    <Button className="bg-montessori-terracotta hover:bg-montessori-terracotta/90 text-white">
                      Enroll Now <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                  <div className="relative">
                    <div className="absolute -top-4 -left-4 w-24 h-24 bg-montessori-terracotta/20 rounded-full"></div>
                    <div className="aspect-[4/3] overflow-hidden rounded-lg shadow-lg relative z-10">
                      <img
                        src="https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                        alt="Preschool children learning"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-montessori-sage/30 rounded-full"></div>
                  </div>
                </div>
              </TabsContent>


            </Tabs>
          </ScrollReveal>
        </div>
      </section>

      {/* Curriculum Section */}
      <section className="py-20 bg-montessori-beige/30">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="font-serif text-3xl font-bold text-center text-montessori-text mb-6">
              Our Learning Areas
            </h2>
            <p className="text-montessori-text/80 text-center max-w-3xl mx-auto mb-16">
              Our curriculum is designed to support early childhood development through play-based learning
              and age-appropriate activities that prepare children for their educational journey.
            </p>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Language Development",
                description: "Building vocabulary, communication skills, and early literacy through stories, songs, and conversation."
              },
              {
                title: "Early Mathematics",
                description: "Introduction to numbers, counting, shapes, and patterns through hands-on activities and games."
              },
              {
                title: "Creative Arts",
                description: "Encouraging self-expression through drawing, painting, music, and imaginative play activities."
              },
              {
                title: "Physical Development",
                description: "Developing gross and fine motor skills through outdoor play, sports, and manipulative activities."
              },
              {
                title: "Social Skills",
                description: "Learning to share, cooperate, and interact positively with peers and adults in group settings."
              },
              {
                title: "Life Skills",
                description: "Building independence through self-care activities, following routines, and simple responsibilities."
              }
            ].map((area, index) => (
              <ScrollReveal key={index} direction="up" delay={index * 0.1}>
                <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 h-full border-t-4 border-montessori-terracotta">
                  <h3 className="font-serif text-xl font-semibold mb-3 text-montessori-text">{area.title}</h3>
                  <p className="text-montessori-text/80">{area.description}</p>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Before/After Care */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <ScrollReveal direction="left">
              <div className="bg-montessori-sage/20 p-8 rounded-lg">
                <div className="flex items-start gap-4 mb-4">
                  <div className="bg-montessori-beige/70 p-3 rounded-full">
                    <Clock className="h-6 w-6 text-montessori-terracotta" />
                  </div>
                  <div>
                    <h3 className="font-serif text-2xl font-bold text-montessori-text mb-2">Before-School Care</h3>
                    <p className="text-montessori-text/80 mb-4">
                      Available from 7:00 AM - 8:30 AM, our before-school program provides a calm start to the day 
                      with gentle activities to ease the transition to the classroom.
                    </p>
                    <ul className="space-y-2 text-montessori-text/80">
                      <li className="flex items-center gap-2">
                        <div className="h-2 w-2 bg-montessori-terracotta rounded-full"></div>
                        <span>Available for all age groups</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="h-2 w-2 bg-montessori-terracotta rounded-full"></div>
                        <span>Additional fee applies</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="h-2 w-2 bg-montessori-terracotta rounded-full"></div>
                        <span>Healthy breakfast option available</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal direction="right" delay={0.2}>
              <div className="bg-montessori-beige/30 p-8 rounded-lg">
                <div className="flex items-start gap-4 mb-4">
                  <div className="bg-montessori-beige/70 p-3 rounded-full">
                    <Calendar className="h-6 w-6 text-montessori-terracotta" />
                  </div>
                  <div>
                    <h3 className="font-serif text-2xl font-bold text-montessori-text mb-2">After-School Care</h3>
                    <p className="text-montessori-text/80 mb-4">
                      Available from 3:30 PM - 6:00 PM, our after-school program provides enrichment activities, 
                      outdoor play, and a nurturing environment until pickup.
                    </p>
                    <ul className="space-y-2 text-montessori-text/80">
                      <li className="flex items-center gap-2">
                        <div className="h-2 w-2 bg-montessori-terracotta rounded-full"></div>
                        <span>Homework support available</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="h-2 w-2 bg-montessori-terracotta rounded-full"></div>
                        <span>Enrichment activities rotate weekly</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="h-2 w-2 bg-montessori-terracotta rounded-full"></div>
                        <span>Healthy snacks provided</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-montessori-sage/10">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="font-serif text-3xl font-bold text-center text-montessori-text mb-16">
              What Parents Say
            </h2>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                quote: "The growth we've seen in our daughter since joining Given Promise Academy has been remarkable. She's more confident, curious, and independent.",
                name: "Jessica T.",
                role: "Parent of Preschool Student"
              },
              {
                quote: "The teachers truly understand each child as an individual. My son's specific interests are nurtured while he also develops in areas where he needs more support.",
                name: "Mark R.",
                role: "Parent of Nursery Student"
              },
              {
                quote: "We couldn't be happier with our decision to enroll our twins here. The community is warm and welcoming, and the education is exceptional.",
                name: "Prya K.",
                role: "Parent of Preschool Students"
              }
            ].map((testimonial, index) => (
              <ScrollReveal key={index} direction="up" delay={index * 0.2}>
                <div className="bg-white p-8 rounded-lg shadow-md h-full border-l-4 border-montessori-terracotta flex flex-col">
                  <div className="text-5xl text-montessori-terracotta/30 mb-4">"</div>
                  <p className="text-montessori-text/80 italic mb-6 flex-grow">{testimonial.quote}</p>
                  <div>
                    <p className="font-semibold text-montessori-text">{testimonial.name}</p>
                    <p className="text-sm text-montessori-text/70">{testimonial.role}</p>
                  </div>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-montessori-beige/40">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="bg-white rounded-xl shadow-lg p-8 md:p-12 text-center max-w-4xl mx-auto">
              <h2 className="font-serif text-3xl font-bold mb-6 text-montessori-text">
                Ready to Enroll Your Child?
              </h2>
              <p className="text-lg text-montessori-text/80 mb-8 max-w-2xl mx-auto">
                Start your child's educational journey today. Contact us to schedule a tour or submit your enrollment application.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button className="bg-montessori-terracotta hover:bg-montessori-terracotta/90 text-white px-6 py-6 text-lg">
                  Apply Now <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button variant="outline" className="border-montessori-sage text-montessori-text hover:bg-montessori-sage/10 px-6 py-6 text-lg">
                  Schedule a Tour
                </Button>
              </div>
            </div>
          </ScrollReveal>
        </div>
      </section>
    </MainLayout>
  );
};

export default Programs;
