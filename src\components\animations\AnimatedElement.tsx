
import { motion, Variants, AnimationControls, useAnimation } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useEffect } from "react";

interface AnimatedElementProps {
  children: React.ReactNode;
  type?: "fade" | "slide" | "scale" | "rotate" | "custom";
  direction?: "up" | "down" | "left" | "right";
  duration?: number;
  delay?: number;
  once?: boolean;
  distance?: number;
  threshold?: number;
  className?: string;
  variants?: Variants;
  whileHover?: "pulse" | "bounce" | "wiggle" | "grow" | "lift" | "glow" | any; // Changed from object to any
  whileTap?: "shrink" | "spin" | any; // Changed from object to any
  stiffness?: number;
  damping?: number;
}

export const AnimatedElement = ({
  children,
  type = "fade",
  direction = "up",
  duration = 0.5,
  delay = 0,
  once = true,
  distance = 50,
  threshold = 0.1,
  className = "",
  variants,
  whileHover,
  whileTap,
  stiffness = 100,
  damping = 10,
}: AnimatedElementProps) => {
  const controls: AnimationControls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: once,
    threshold,
    rootMargin: "-50px 0px",
  });

  useEffect(() => {
    if (inView) {
      controls.start("visible");
    } else if (!once) {
      controls.start("hidden");
    }
  }, [controls, inView, once]);

  // Define basic variants based on type and direction
  let defaultVariants: Variants = {};

  // Direction offset settings
  let offset = {};
  if (direction === "up") offset = { y: distance };
  if (direction === "down") offset = { y: -distance };
  if (direction === "left") offset = { x: distance };
  if (direction === "right") offset = { x: -distance };

  // Animation type settings
  switch (type) {
    case "fade":
      defaultVariants = {
        hidden: { opacity: 0 },
        visible: { 
          opacity: 1, 
          transition: { duration, delay, type: "spring", stiffness, damping } 
        }
      };
      break;
    case "slide":
      defaultVariants = {
        hidden: { opacity: 0, ...offset },
        visible: {
          opacity: 1,
          x: 0,
          y: 0,
          transition: { duration, delay, type: "spring", stiffness, damping }
        }
      };
      break;
    case "scale":
      defaultVariants = {
        hidden: { opacity: 0, scale: 0.5, ...offset },
        visible: {
          opacity: 1,
          scale: 1,
          x: 0,
          y: 0,
          transition: { duration, delay, type: "spring", stiffness, damping }
        }
      };
      break;
    case "rotate":
      defaultVariants = {
        hidden: { opacity: 0, rotate: direction === "left" ? -90 : 90, ...offset },
        visible: {
          opacity: 1,
          rotate: 0,
          x: 0,
          y: 0,
          transition: { duration, delay, type: "spring", stiffness, damping }
        }
      };
      break;
    default:
      defaultVariants = {
        hidden: { opacity: 0 },
        visible: { 
          opacity: 1, 
          transition: { duration, delay, type: "spring", stiffness, damping } 
        }
      };
  }

  // Hover animations
  const getHoverAnimation = () => {
    if (typeof whileHover === 'object') return whileHover as any;

    switch (whileHover) {
      case "pulse":
        return { scale: [1, 1.05, 1], transition: { repeat: Infinity, duration: 1.5 } };
      case "bounce":
        return { y: [0, -10, 0], transition: { repeat: Infinity, duration: 1 } };
      case "wiggle":
        return { rotate: [0, -5, 5, -5, 0], transition: { repeat: Infinity, duration: 0.5 } };
      case "grow":
        return { scale: 1.05 };
      case "lift":
        return { y: -10, boxShadow: "0 10px 25px rgba(0,0,0,0.1)" };
      case "glow":
        return { boxShadow: "0 0 15px rgba(224,122,95,0.5)" };
      default:
        return {};
    }
  };

  // Tap animations
  const getTapAnimation = () => {
    if (typeof whileTap === 'object') return whileTap as any;
    
    switch (whileTap) {
      case "shrink":
        return { scale: 0.9 };
      case "spin":
        return { rotate: 360, transition: { duration: 0.5 } };
      default:
        return {};
    }
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={variants || defaultVariants}
      whileHover={whileHover ? getHoverAnimation() : undefined}
      whileTap={whileTap ? getTapAnimation() : undefined}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedElement;
