
import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import EnrollmentForm from "../forms/EnrollmentForm";

type EnrollmentDialogProps = {
  triggerClassName?: string;
  children?: React.ReactNode;
};

const EnrollmentDialog = ({ triggerClassName, children }: EnrollmentDialogProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" className={`border-montessori-sage text-montessori-text hover:bg-montessori-sage/10 px-8 py-6 text-lg ${triggerClassName || ""}`}>
            Enroll Now
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-serif text-montessori-text">Enrollment Application</DialogTitle>
          <DialogDescription>
            Begin your child's educational journey with us by completing this enrollment application.
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          <EnrollmentForm />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EnrollmentDialog;
