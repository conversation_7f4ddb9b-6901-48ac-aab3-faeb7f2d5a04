import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getAuth } from 'firebase/auth';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBKeLhuWt7DEV8QBzZH4d4cwvBLAzlaABA",
  authDomain: "givenpromiseacademy-f0de7.firebaseapp.com",
  projectId: "givenpromiseacademy-f0de7",
  storageBucket: "givenpromiseacademy-f0de7.firebasestorage.app",
  messagingSenderId: "262414822644",
  appId: "1:262414822644:web:abae9a1d89bb7d1b3e02a1",
  measurementId: "G-KHBDGKN1FY"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);
export const storage = getStorage(app);
export const auth = getAuth(app);

export default app;
