
import { motion } from "framer-motion";
import MainLayout from "@/components/layout/MainLayout";
import ScrollReveal from "@/components/animations/ScrollReveal";

const History = () => {
  const timelineItems = [
    {
      year: "2010",
      title: "Foundation of Given Promise Academy",
      description: "Given Promise Academy was founded by a team of dedicated early childhood educators committed to providing quality nursery and preschool education.",
      image: "https://images.unsplash.com/photo-1544717297-fa95b6ee9643?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      year: "2012",
      title: "First Graduation Class",
      description: "Our first group of preschoolers graduated and successfully transitioned to primary school, demonstrating our effective early learning approach.",
      image: "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      year: "2015",
      title: "Facility Expansion",
      description: "Expanded our facilities to include dedicated outdoor play areas and specialized learning spaces for different age groups.",
      image: "https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      year: "2018",
      title: "Community Recognition",
      description: "Received recognition from the local education authority for excellence in early childhood education and community service.",
      image: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      year: "2020",
      title: "Digital Learning Integration",
      description: "Successfully adapted to include digital learning tools while maintaining our hands-on, play-based approach during challenging times.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      year: "2022",
      title: "150 Families Milestone",
      description: "Reached the milestone of serving 150 families in our community, demonstrating the trust parents place in our educational approach.",
      image: "https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    },
    {
      year: "2024",
      title: "Continued Excellence",
      description: "Today, Given Promise Academy continues to provide quality early childhood education, preparing children for their educational journey with confidence and joy.",
      image: "https://images.unsplash.com/photo-1544717297-fa95b6ee9643?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
    }
  ];

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-b from-montessori-beige to-white">
        {/* Hero Section */}
        <motion.section 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className="relative h-[60vh] flex items-center justify-center overflow-hidden"
        >
          <div className="absolute inset-0 bg-montessori-terracotta/10" />
          <div className="container mx-auto px-4 relative z-10">
            <motion.h1 
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.8 }}
              className="text-4xl md:text-6xl font-serif text-center text-montessori-text mb-6"
            >
              Our Educational Journey
            </motion.h1>
            <motion.p 
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.8 }}
              className="text-xl text-center text-montessori-text/80 max-w-2xl mx-auto"
            >
              Discover the rich history of early childhood education and how it continues to shape young minds today
            </motion.p>
          </div>
        </motion.section>

        {/* Timeline Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="space-y-20">
              {timelineItems.map((item, index) => (
                <ScrollReveal
                  key={item.year}
                  direction={index % 2 === 0 ? "left" : "right"}
                  className="flex flex-col md:flex-row items-center gap-8 md:gap-12"
                >
                  <div className={`w-full md:w-1/2 ${index % 2 === 0 ? 'md:order-1' : 'md:order-2'}`}>
                    <div className="w-full h-64 overflow-hidden rounded-lg shadow-lg">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                  <div className={`w-full md:w-1/2 ${index % 2 === 0 ? 'md:order-2' : 'md:order-1'}`}>
                    <div className="space-y-4">
                      <span className="text-montessori-terracotta font-bold text-xl">
                        {item.year}
                      </span>
                      <h3 className="text-2xl font-serif text-montessori-text">
                        {item.title}
                      </h3>
                      <p className="text-montessori-text/80 leading-relaxed">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </ScrollReveal>
              ))}
            </div>
          </div>
        </section>

        {/* Philosophy Section */}
        <ScrollReveal>
          <section className="py-20 bg-montessori-sage/10">
            <div className="container mx-auto px-4">
              <div className="max-w-3xl mx-auto text-center">
                <h2 className="text-3xl md:text-4xl font-serif text-montessori-text mb-8">
                  Our Educational Philosophy
                </h2>
                <p className="text-montessori-text/80 leading-relaxed mb-6">
                  At Given Promise Academy, our approach to early childhood education is based on understanding each child's unique developmental journey. We recognize that children have natural periods when they are most ready to learn specific skills and develop particular abilities.
                </p>
                <p className="text-montessori-text/80 leading-relaxed mb-6">
                  Modern early childhood education emphasizes play-based learning, social-emotional development, and respect for each child's unique developmental journey. Quality nursery and preschool programs create nurturing environments where children can explore, learn, and grow.
                </p>
                <p className="text-montessori-text/80 leading-relaxed">
                  Today, early childhood education continues to evolve globally, with programs serving children from all backgrounds. Quality early learning has proven effective in preparing children for their educational journey and building strong foundations for lifelong learning.
                </p>
              </div>
            </div>
          </section>
        </ScrollReveal>
      </div>
    </MainLayout>
  );
};

export default History;
