
import { useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { collection, query, where, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { BlogPost } from "@/types/firebase";
import { ArrowLeft } from "lucide-react";

const BlogPostPage = () => {
  const { slug } = useParams<{ slug: string }>();

  const { data: post, isLoading, error } = useQuery({
    queryKey: ["blog-post", slug],
    queryFn: async () => {
      const q = query(
        collection(db, "blog_posts"),
        where("slug", "==", slug),
        where("published", "==", true)
      );
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        throw new Error("Post not found");
      }

      const doc = querySnapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as BlogPost;
    }
  });

  useEffect(() => {
    // Scroll to top when post loads
    window.scrollTo(0, 0);
  }, [post]);

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <Link to="/blog" className="inline-flex items-center text-montessori-terracotta hover:text-montessori-terracotta/80 mb-6">
            <ArrowLeft size={18} className="mr-2" />
            Back to all articles
          </Link>

          {isLoading ? (
            <div className="text-center py-10">Loading article...</div>
          ) : error ? (
            <Card>
              <CardContent className="p-6">
                <h1 className="text-2xl font-serif font-bold text-montessori-text mb-4">Article Not Found</h1>
                <p className="mb-6">Sorry, the article you're looking for doesn't exist or has been removed.</p>
                <Button asChild className="bg-montessori-terracotta">
                  <Link to="/blog">Return to Blog</Link>
                </Button>
              </CardContent>
            </Card>
          ) : post ? (
            <article className="bg-white rounded-lg shadow-md overflow-hidden">
              {post.image_url && (
                <div className="relative h-64 md:h-96 w-full">
                  <img
                    src={post.image_url}
                    alt={post.title}
                    className="absolute inset-0 w-full h-full object-cover"
                  />
                </div>
              )}
              <div className="p-6 md:p-8">
                <div className="flex items-center gap-2 text-sm text-montessori-sage mb-4">
                  <span>{post.category}</span>
                  <span className="inline-block h-1 w-1 rounded-full bg-montessori-sage"></span>
                  <span>{new Date(post.created_at).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}</span>
                </div>
                <h1 className="text-3xl md:text-4xl font-serif font-bold text-montessori-text mb-6">{post.title}</h1>
                
                <div className="prose max-w-none">
                  {post.content.split('\n').map((paragraph, index) => (
                    paragraph ? <p key={index} className="mb-4">{paragraph}</p> : <br key={index} />
                  ))}
                </div>
              </div>
            </article>
          ) : null}
        </div>
      </div>
    </MainLayout>
  );
};

export default BlogPostPage;
