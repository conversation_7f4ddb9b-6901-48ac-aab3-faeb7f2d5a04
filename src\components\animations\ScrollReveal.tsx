
import React, { useEffect, useRef } from "react";
import { useInView } from "react-intersection-observer";
import { motion, useAnimation, Variants } from "framer-motion";

interface ScrollRevealProps {
  children: React.ReactNode;
  width?: "full" | "auto";
  delay?: number;
  duration?: number;
  direction?: "up" | "down" | "left" | "right" | "none";
  distance?: number;
  once?: boolean;
  className?: string;
  scale?: number;
  rotate?: number;
  easing?: string;
  staggerChildren?: boolean;
  staggerDelay?: number;
}

export const ScrollReveal = ({
  children,
  width = "full",
  delay = 0,
  duration = 0.5,
  direction = "up",
  distance = 50,
  once = true,
  className = "",
  scale = 1,
  rotate = 0,
  easing = "easeOut",
  staggerChildren = false,
  staggerDelay = 0.1,
}: ScrollRevealProps) => {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: once,
    threshold: 0.1,
    rootMargin: "-50px 0px",
  });

  useEffect(() => {
    if (inView) {
      controls.start("visible");
    } else if (!once) {
      controls.start("hidden");
    }
  }, [controls, inView, once]);

  // Set the direction of the animation
  let directionData = {};
  switch (direction) {
    case "up":
      directionData = { y: distance };
      break;
    case "down":
      directionData = { y: -distance };
      break;
    case "left":
      directionData = { x: distance };
      break;
    case "right":
      directionData = { x: -distance };
      break;
    default:
      directionData = {};
  }

  // Define the animation variants
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerChildren ? staggerDelay : 0,
        delayChildren: delay,
      }
    }
  };

  const itemVariants: Variants = {
    hidden: {
      opacity: 0,
      scale: scale !== 1 ? 0.9 : 1,
      rotate: rotate !== 0 ? -rotate : 0,
      ...directionData,
    },
    visible: {
      opacity: 1,
      scale: 1,
      rotate: 0,
      y: 0,
      x: 0,
      transition: {
        duration,
        delay: staggerChildren ? 0 : delay,
        ease: easing,
      },
    },
  };

  return staggerChildren ? (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={containerVariants}
      className={`${width === "full" ? "w-full" : ""} ${className}`}
    >
      {React.Children.map(children, (child, i) => (
        <motion.div key={i} variants={itemVariants} className="w-full">
          {child}
        </motion.div>
      ))}
    </motion.div>
  ) : (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={itemVariants}
      className={`${width === "full" ? "w-full" : ""} ${className}`}
    >
      {children}
    </motion.div>
  );
};

export default ScrollReveal;
