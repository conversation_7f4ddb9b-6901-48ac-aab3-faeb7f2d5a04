
import { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { submitContactMessage } from "@/utils/firebaseUtils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Phone, Mail, MapPin, Clock, Send, ArrowRight } from "lucide-react";
import { toast } from "sonner";
import { motion } from "framer-motion";
import ScrollReveal from "@/components/animations/ScrollReveal";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await submitContactMessage({
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        subject: formData.subject,
        message: formData.message
      });

      toast.success("Thank you for your message! We'll be in touch soon.", {
        description: "A member of our team will respond within 24-48 hours.",
        duration: 5000
      });

      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: ""
      });
    } catch (error: any) {
      toast.error(error.message || "Failed to send message");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-to-b from-montessori-beige/40 to-white">
        <div className="container mx-auto px-4">
          <ScrollReveal direction="up" delay={0.2}>
            <h1 className="font-serif text-4xl md:text-5xl lg:text-6xl font-bold text-center text-montessori-text mb-6">
              Get In <span className="text-montessori-terracotta">Touch</span>
            </h1>
            <p className="text-montessori-text/80 text-lg md:text-xl text-center max-w-3xl mx-auto mb-8">
              We'd love to hear from you! Whether you have questions about our nursery and preschool programs,
              want to schedule a visit, or are ready to enroll your child, our team is here to help.
            </p>
          </ScrollReveal>
        </div>
      </section>

      {/* Contact Information and Form Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12">
            {/* Contact Information */}
            <div className="lg:col-span-2">
              <ScrollReveal direction="left">
                <h2 className="font-serif text-3xl font-bold text-montessori-text mb-8">
                  Contact Information
                </h2>
                
                <div className="space-y-8">
                  <div className="flex items-start gap-4">
                    <div className="bg-montessori-beige/50 p-3 rounded-full">
                      <Phone className="h-6 w-6 text-montessori-terracotta" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg text-montessori-text mb-1">Phone</h3>
                      <p className="text-montessori-text/80">+255 754 123 456</p>
                      <p className="text-montessori-text/70 text-sm">Mon-Fri: 7:30 AM - 5:00 PM</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-montessori-beige/50 p-3 rounded-full">
                      <Mail className="h-6 w-6 text-montessori-terracotta" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg text-montessori-text mb-1">Email</h3>
                      <p className="text-montessori-text/80"><EMAIL></p>
                      <p className="text-montessori-text/70 text-sm">We'll respond within 24 hours</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-montessori-beige/50 p-3 rounded-full">
                      <MapPin className="h-6 w-6 text-montessori-terracotta" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg text-montessori-text mb-1">Address</h3>
                      <p className="text-montessori-text/80">P.O. Box 1234</p>
                      <p className="text-montessori-text/80">Dar es Salaam, Tanzania</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-montessori-beige/50 p-3 rounded-full">
                      <Clock className="h-6 w-6 text-montessori-terracotta" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg text-montessori-text mb-1">Office Hours</h3>
                      <p className="text-montessori-text/80">Monday - Friday: 7:30 AM - 5:00 PM</p>
                      <p className="text-montessori-text/80">Saturday: 8:00 AM - 12:00 PM (Visits by appointment)</p>
                      <p className="text-montessori-text/80">Sunday: Closed</p>
                    </div>
                  </div>
                </div>

                <div className="mt-12">
                  <h3 className="font-semibold text-lg text-montessori-text mb-4">Connect With Us</h3>
                  <div className="flex space-x-4">
                    <a href="https://facebook.com/givenpromiseacademy" target="_blank" rel="noopener noreferrer" className="bg-montessori-beige/50 p-3 rounded-full hover:bg-montessori-terracotta/20 transition-colors duration-300">
                      <svg className="h-6 w-6 text-montessori-terracotta" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                      </svg>
                    </a>
                    <a href="https://instagram.com/givenpromiseacademy" target="_blank" rel="noopener noreferrer" className="bg-montessori-beige/50 p-3 rounded-full hover:bg-montessori-terracotta/20 transition-colors duration-300">
                      <svg className="h-6 w-6 text-montessori-terracotta" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" />
                      </svg>
                    </a>
                    <a href="https://twitter.com/givenpromiseacademy" target="_blank" rel="noopener noreferrer" className="bg-montessori-beige/50 p-3 rounded-full hover:bg-montessori-terracotta/20 transition-colors duration-300">
                      <svg className="h-6 w-6 text-montessori-terracotta" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                      </svg>
                    </a>
                  </div>
                </div>
              </ScrollReveal>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-3">
              <ScrollReveal direction="right" delay={0.3}>
                <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-lg p-8 border border-montessori-sage/20">
                  <h2 className="font-serif text-3xl font-bold text-montessori-text mb-6">
                    Send Us a Message
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="space-y-2">
                      <label htmlFor="name" className="block text-sm font-medium text-montessori-text">
                        Your Name <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        placeholder="John Doe"
                        className="border-montessori-sage/30 focus:border-montessori-terracotta"
                      />
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="email" className="block text-sm font-medium text-montessori-text">
                        Email Address <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        placeholder="<EMAIL>"
                        className="border-montessori-sage/30 focus:border-montessori-terracotta"
                      />
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="phone" className="block text-sm font-medium text-montessori-text">
                        Phone Number
                      </label>
                      <Input
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        placeholder="+255*********"
                        className="border-montessori-sage/30 focus:border-montessori-terracotta"
                      />
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="subject" className="block text-sm font-medium text-montessori-text">
                        Subject <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleChange}
                        required
                        placeholder="e.g., Enrollment Question"
                        className="border-montessori-sage/30 focus:border-montessori-terracotta"
                      />
                    </div>
                  </div>

                  <div className="space-y-2 mb-6">
                    <label htmlFor="message" className="block text-sm font-medium text-montessori-text">
                      Message <span className="text-red-500">*</span>
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      placeholder="Type your message here..."
                      className="min-h-[150px] border-montessori-sage/30 focus:border-montessori-terracotta"
                    />
                  </div>

                  <Button 
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-montessori-terracotta hover:bg-montessori-terracotta/90 text-white w-full py-6"
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </span>
                    ) : (
                      <span className="flex items-center">
                        Send Message <Send className="ml-2 h-5 w-5" />
                      </span>
                    )}
                  </Button>

                </form>
              </ScrollReveal>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-16 bg-montessori-beige/20">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="font-serif text-3xl font-bold text-center text-montessori-text mb-8">
              Visit Our Academy
            </h2>
            <div className="rounded-lg overflow-hidden shadow-lg h-[400px] md:h-[500px]">
              <iframe
                 src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3966.8925278678093!2d35.785237873856865!3d-6.145134960236798!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x184de5f5f91c48f9%3A0x9ef64aec3fc1e96e!2sGiven%20Promise%20Academy!5e0!3m2!1sen!2stz!4v1754135487379!5m2!1sen!2stz"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
              ></iframe>
            </div>
          </ScrollReveal>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="font-serif text-3xl font-bold text-center text-montessori-text mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-montessori-text/80 text-center max-w-3xl mx-auto mb-12">
              Find quick answers to common questions about our nursery and preschool programs, enrollment process, and policies.
            </p>
          </ScrollReveal>

          <div className="max-w-4xl mx-auto space-y-6">
            {[
              {
                question: "What age groups do you accept?",
                answer: "We accept children from 2-6 years old. Our Nursery program is for ages 2-3, and our Preschool program is for ages 4-6. We ensure age-appropriate activities and learning experiences for each group."
              },
              {
                question: "What is the enrollment process?",
                answer: "Start by scheduling a visit to see our facilities and meet our teachers. Complete an application form, provide required documents, and attend a brief interview. Once accepted, you'll receive enrollment paperwork and orientation information."
              },
              {
                question: "What are your teacher qualifications?",
                answer: "All our teachers are qualified in early childhood education with relevant certifications. They have experience working with young children and receive ongoing professional development training."
              },
              {
                question: "Do you provide meals and snacks?",
                answer: "We provide healthy snacks throughout the day. Parents pack lunches for their children, and we can accommodate special dietary requirements with advance notice."
              },
              {
                question: "What are your safety and security measures?",
                answer: "Child safety is our top priority. We have secure entry systems, trained staff in first aid, regular safety drills, and comprehensive child protection policies in place."
              }
            ].map((faq, index) => (
              <ScrollReveal key={index} direction={index % 2 === 0 ? "left" : "right"} delay={index * 0.1}>
                <div className="bg-montessori-beige/20 rounded-lg p-6 hover:shadow-md transition-shadow duration-300">
                  <h3 className="font-serif text-xl font-semibold text-montessori-text mb-3">{faq.question}</h3>
                  <p className="text-montessori-text/80">{faq.answer}</p>
                </div>
              </ScrollReveal>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-montessori-text/80 mb-4">
              Still have questions? We're here to help!
            </p>
            <Button className="bg-montessori-terracotta hover:bg-montessori-terracotta/90 text-white">
              Contact Us <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </section>
    </MainLayout>
  );
};

export default Contact;
