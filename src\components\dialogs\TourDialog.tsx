
import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { MapPin } from "lucide-react";
import TourScheduleForm from "../forms/TourScheduleForm";

type TourDialogProps = {
  triggerClassName?: string;
  children?: React.ReactNode;
};

const TourDialog = ({ triggerClassName, children }: TourDialogProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {children || (
          <Button className={`bg-montessori-terracotta hover:bg-montessori-terracotta/90 text-white px-8 py-6 text-lg ${triggerClassName || ""}`}>
            Schedule Now <MapPin className="ml-2 h-5 w-5" />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-serif text-montessori-text">Schedule Your Tour</DialogTitle>
          <DialogDescription>
            Visit our campus and see firsthand how our nurturing environment supports children's natural development.
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          <TourScheduleForm />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TourDialog;
