
import { useState, useEffect } from "react";
import AdminPanel from "@/components/admin/AdminPanel";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";
import { signInWithEmailAndPassword, onAuthStateChanged, signOut } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

const Admin = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [firebaseError, setFirebaseError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    checkSession();
  }, []);

  const checkSession = async () => {
    try {
      onAuthStateChanged(auth, (user) => {
        setIsLoggedIn(!!user);
      });
    } catch (error) {
      console.error("Error checking session:", error);
      setFirebaseError("Could not connect to Firebase. Please check your configuration.");
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await signInWithEmailAndPassword(auth, username, password);
      setIsLoggedIn(true);
      toast.success("Successfully logged in!");
    } catch (error: any) {
      toast.error(error.message || "Failed to login");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-montessori-beige/30 p-4 relative">
        <Link to="/">
          <Button 
            variant="ghost" 
            className="absolute top-4 left-4 gap-2"
          >
            <ArrowLeft size={20} />
            Back to Home
          </Button>
        </Link>
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-serif font-bold text-montessori-text">
                Given Promise Academy
              </h1>
              <p className="text-montessori-text/70 mt-2">Admin Login</p>
            </div>
            
            {firebaseError && (
              <div className="mb-6 p-4 bg-red-100 text-red-800 rounded-md">
                <p className="font-medium">Configuration Error:</p>
                <p>{firebaseError}</p>
                <p className="text-sm mt-2">Please make sure you've set the Firebase environment variables.</p>
              </div>
            )}
            
            <form onSubmit={handleLogin}>
              <div className="space-y-4">
                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-montessori-text mb-1">
                    Email
                  </label>
                  <input
                    id="username"
                    type="email"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="w-full px-3 py-2 border border-montessori-sage/40 rounded-md"
                    placeholder="Enter your email"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-montessori-text mb-1">
                    Password
                  </label>
                  <input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-3 py-2 border border-montessori-sage/40 rounded-md"
                    placeholder="Enter your password"
                    required
                  />
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full bg-montessori-terracotta hover:bg-montessori-terracotta/90"
                  disabled={isLoading || !!firebaseError}
                >
                  {isLoading ? "Logging in..." : "Log In"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <AdminPanel />
    </div>
  );
};

export default Admin;
