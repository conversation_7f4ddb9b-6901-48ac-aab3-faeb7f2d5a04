import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BellDot, MessageSquare, Calendar, FileText, Edit, Trash, Upload, ArrowLeft, Megaphone } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { useIsMobile } from "@/hooks/use-mobile";
import { GalleryPhoto, ContactMessage, TourSchedule, EnrollmentForm, Announcement } from "@/types/firebase";
import {
  getGalleryPhotos,
  uploadGalleryPhoto,
  deleteGalleryPhoto,
  updateGalleryPhoto,
  getContactMessages,
  markMessageAsRead,
  deleteContactMessage,
  getTourSchedules,
  updateTourScheduleStatus,
  deleteTourSchedule,
  getEnrollmentForms,
  updateEnrollmentFormStatus,
  deleteEnrollmentForm,
  getAnnouncements
} from "@/utils/firebaseUtils";
import BlogManager from "./BlogManager";
import AnnouncementManager from "./AnnouncementManager";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

const AdminPanel = () => {
  const [activeTab, setActiveTab] = useState("gallery");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isMobile = useIsMobile();
  
  const [photos, setPhotos] = useState<GalleryPhoto[]>([]);
  const [photoTitle, setPhotoTitle] = useState("");
  const [photoCategory, setPhotoCategory] = useState("");
  const [photoDescription, setPhotoDescription] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isEditingPhoto, setIsEditingPhoto] = useState(false);
  const [editingPhotoId, setEditingPhotoId] = useState<string | null>(null);
  
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  
  const [scheduleRequests, setScheduleRequests] = useState<TourSchedule[]>([]);
  
  const [enrollmentForms, setEnrollmentForms] = useState<EnrollmentForm[]>([]);

  const [isLoadingGallery, setIsLoadingGallery] = useState(true);
  const [isLoadingMessages, setIsLoadingMessages] = useState(true);
  const [isLoadingSchedules, setIsLoadingSchedules] = useState(true);
  const [isLoadingEnrollments, setIsLoadingEnrollments] = useState(true);

  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{ type: 'photo' | 'message' | 'schedule' | 'enrollment', id: string } | null>(null);

  const [notificationCounts, setNotificationCounts] = useState({
    messages: 0,
    tours: 0,
    enrollments: 0
  });

  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoadingAnnouncements, setIsLoadingAnnouncements] = useState(true);

  useEffect(() => {
    if (activeTab === "gallery") {
      loadGalleryPhotos();
    } else if (activeTab === "messages") {
      loadContactMessages();
    } else if (activeTab === "schedule") {
      loadTourSchedules();
    } else if (activeTab === "enrollment") {
      loadEnrollmentForms();
    } else if (activeTab === "announcements") {
      loadAnnouncements();
    }
  }, [activeTab]);

  useEffect(() => {
    setNotificationCounts({
      messages: messages.filter(msg => !msg.read).length,
      tours: scheduleRequests.filter(req => req.status === "pending").length,
      enrollments: enrollmentForms.filter(form => form.status === "pending").length
    });
  }, [messages, scheduleRequests, enrollmentForms]);

  const loadGalleryPhotos = async () => {
    setIsLoadingGallery(true);
    try {
      const data = await getGalleryPhotos();
      setPhotos(data);
    } catch (error: any) {
      toast.error(`Failed to load gallery photos: ${error.message}`);
    } finally {
      setIsLoadingGallery(false);
    }
  };

  const loadContactMessages = async () => {
    setIsLoadingMessages(true);
    try {
      const data = await getContactMessages();
      setMessages(data);
    } catch (error: any) {
      toast.error(`Failed to load messages: ${error.message}`);
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const loadTourSchedules = async () => {
    setIsLoadingSchedules(true);
    try {
      const data = await getTourSchedules();
      setScheduleRequests(data);
    } catch (error: any) {
      toast.error(`Failed to load tour schedules: ${error.message}`);
    } finally {
      setIsLoadingSchedules(false);
    }
  };

  const loadEnrollmentForms = async () => {
    setIsLoadingEnrollments(true);
    try {
      const data = await getEnrollmentForms();
      setEnrollmentForms(data);
    } catch (error: any) {
      toast.error(`Failed to load enrollment forms: ${error.message}`);
    } finally {
      setIsLoadingEnrollments(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleUploadPhoto = async () => {
    if (!selectedFile) {
      toast.error("Please select a file to upload");
      return;
    }

    if (!photoTitle || !photoCategory) {
      toast.error("Please provide title and category");
      return;
    }

    setIsUploading(true);
    try {
      await uploadGalleryPhoto(selectedFile, photoTitle, photoDescription, photoCategory);
      await loadGalleryPhotos();
      toast.success("Photo uploaded successfully");
      
      setPhotoTitle("");
      setPhotoCategory("");
      setPhotoDescription("");
      setSelectedFile(null);
      if (fileInputRef.current) fileInputRef.current.value = "";
    } catch (error: any) {
      toast.error(`Failed to upload photo: ${error.message}`);
    } finally {
      setIsUploading(false);
    }
  };

  const handleEditPhoto = async () => {
    if (!editingPhotoId) return;

    try {
      await updateGalleryPhoto(editingPhotoId, {
        title: photoTitle,
        description: photoDescription,
        category: photoCategory
      });
      
      await loadGalleryPhotos();
      toast.success("Photo updated successfully");
      
      cancelEditPhoto();
    } catch (error: any) {
      toast.error(`Failed to update photo: ${error.message}`);
    }
  };

  const startEditPhoto = (photo: GalleryPhoto) => {
    setPhotoTitle(photo.title);
    setPhotoCategory(photo.category);
    setPhotoDescription(photo.description);
    setEditingPhotoId(photo.id);
    setIsEditingPhoto(true);
  };

  const cancelEditPhoto = () => {
    setPhotoTitle("");
    setPhotoCategory("");
    setPhotoDescription("");
    setEditingPhotoId(null);
    setIsEditingPhoto(false);
    setSelectedFile(null);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const confirmDeleteItem = (type: 'photo' | 'message' | 'schedule' | 'enrollment', id: string) => {
    setItemToDelete({ type, id });
    setShowDeleteConfirmation(true);
  };

  const handleDeleteConfirmed = async () => {
    if (!itemToDelete) return;

    try {
      switch (itemToDelete.type) {
        case 'photo':
          await deleteGalleryPhoto(itemToDelete.id);
          await loadGalleryPhotos();
          break;
        case 'message':
          await deleteContactMessage(itemToDelete.id);
          await loadContactMessages();
          break;
        case 'schedule':
          await deleteTourSchedule(itemToDelete.id);
          await loadTourSchedules();
          break;
        case 'enrollment':
          await deleteEnrollmentForm(itemToDelete.id);
          await loadEnrollmentForms();
          break;
      }
      toast.success("Item deleted successfully");
      setShowDeleteConfirmation(false);
      setItemToDelete(null);
    } catch (error: any) {
      toast.error(`Failed to delete item: ${error.message}`);
    }
  };

  const handleMarkAsRead = async (id: string) => {
    try {
      await markMessageAsRead(id);
      setMessages(messages.map(msg => 
        msg.id === id ? { ...msg, read: true } : msg
      ));
      toast.success("Message marked as read");
    } catch (error: any) {
      toast.error(`Failed to update message: ${error.message}`);
    }
  };

  const handleUpdateScheduleStatus = async (id: string, newStatus: TourSchedule['status']) => {
    try {
      await updateTourScheduleStatus(id, newStatus);
      setScheduleRequests(scheduleRequests.map(req => 
        req.id === id ? { ...req, status: newStatus } : req
      ));
      toast.success(`Tour request ${newStatus}`);
    } catch (error: any) {
      toast.error(`Failed to update tour status: ${error.message}`);
    }
  };

  const handleUpdateEnrollmentStatus = async (id: string, newStatus: EnrollmentForm['status']) => {
    try {
      await updateEnrollmentFormStatus(id, newStatus);
      setEnrollmentForms(enrollmentForms.map(form => 
        form.id === id ? { ...form, status: newStatus } : form
      ));
      toast.success(`Enrollment ${newStatus}`);
    } catch (error: any) {
      toast.error(`Failed to update enrollment status: ${error.message}`);
    }
  };

  const loadAnnouncements = async () => {
    setIsLoadingAnnouncements(true);
    try {
      const data = await getAnnouncements();
      setAnnouncements(data);
    } catch (error: any) {
      toast.error(`Failed to load announcements: ${error.message}`);
    } finally {
      setIsLoadingAnnouncements(false);
    }
  };

  return (
    <div className="min-h-screen">
      <div className="p-4 md:p-6 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col gap-4 mb-6">
            <div className="flex items-center">
              <Link to="/">
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="flex items-center text-montessori-text mr-2" 
                >
                  <ArrowLeft size={18} className="mr-1" />
                  <span className="hidden sm:inline">Back to Home</span>
                </Button>
              </Link>
            </div>
            
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <h1 className="text-2xl md:text-3xl font-serif font-bold text-montessori-text">Admin Dashboard</h1>
              <div className="flex flex-wrap items-center gap-2">
                {notificationCounts.messages > 0 && (
                  <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
                    <MessageSquare className="h-5 w-5 text-montessori-terracotta mr-1" />
                    <span className="text-sm font-medium">{notificationCounts.messages} unread</span>
                  </div>
                )}
                {(notificationCounts.tours > 0 || notificationCounts.enrollments > 0) && (
                  <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
                    <BellDot className="h-5 w-5 text-montessori-terracotta mr-1" />
                    <span className="text-sm font-medium">
                      {notificationCounts.tours + notificationCounts.enrollments} pending
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="mb-4 md:mb-8 bg-white p-1 rounded-lg shadow-sm overflow-x-auto flex w-full">
              <TabsTrigger value="gallery" className="flex-1 min-w-[100px] data-[state=active]:bg-montessori-beige/20">
                <FileText className={`${isMobile ? "mr-0" : "mr-2"} h-5 w-5`} /> 
                <span className={isMobile ? "hidden" : "inline"}>Gallery</span>
              </TabsTrigger>
              <TabsTrigger value="messages" className="flex-1 min-w-[100px] data-[state=active]:bg-montessori-beige/20">
                <MessageSquare className={`${isMobile ? "mr-0" : "mr-2"} h-5 w-5`} />
                <span className={isMobile ? "hidden" : "inline"}>Messages</span>
                {notificationCounts.messages > 0 && (
                  <Badge variant="destructive" className={isMobile ? "ml-0" : "ml-2"}>
                    {notificationCounts.messages}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="schedule" className="flex-1 min-w-[100px] data-[state=active]:bg-montessori-beige/20">
                <Calendar className={`${isMobile ? "mr-0" : "mr-2"} h-5 w-5`} />
                <span className={isMobile ? "hidden" : "inline"}>Tours</span>
                {notificationCounts.tours > 0 && (
                  <Badge variant="destructive" className={isMobile ? "ml-0" : "ml-2"}>
                    {notificationCounts.tours}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="enrollment" className="flex-1 min-w-[100px] data-[state=active]:bg-montessori-beige/20">
                <FileText className={`${isMobile ? "mr-0" : "mr-2"} h-5 w-5`} />
                <span className={isMobile ? "hidden" : "inline"}>Enrollment</span>
                {notificationCounts.enrollments > 0 && (
                  <Badge variant="destructive" className={isMobile ? "ml-0" : "ml-2"}>
                    {notificationCounts.enrollments}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="blog" className="flex-1 min-w-[100px] data-[state=active]:bg-montessori-beige/20">
                <FileText className={`${isMobile ? "mr-0" : "mr-2"} h-5 w-5`} />
                <span className={isMobile ? "hidden" : "inline"}>Blog</span>
              </TabsTrigger>
              <TabsTrigger value="announcements" className="flex-1 min-w-[100px] data-[state=active]:bg-montessori-beige/20">
                <Megaphone className={`${isMobile ? "mr-0" : "mr-2"} h-5 w-5`} />
                <span className={isMobile ? "hidden" : "inline"}>Announcements</span>
              </TabsTrigger>
            </TabsList>
            
            
            <TabsContent value="gallery" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>{isEditingPhoto ? "Edit Photo" : "Upload New Photo"}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="photo-title">Title</Label>
                        <Input 
                          id="photo-title" 
                          placeholder="Enter photo title" 
                          value={photoTitle}
                          onChange={(e) => setPhotoTitle(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="photo-category">Category</Label>
                        <Select value={photoCategory} onValueChange={setPhotoCategory}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="classroom">Classroom</SelectItem>
                            <SelectItem value="outdoor">Outdoor</SelectItem>
                            <SelectItem value="activities">Activities</SelectItem>
                            <SelectItem value="events">Events</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="photo-description">Description</Label>
                      <Textarea 
                        id="photo-description" 
                        placeholder="Enter photo description" 
                        value={photoDescription}
                        onChange={(e) => setPhotoDescription(e.target.value)}
                      />
                    </div>
                    {!isEditingPhoto && (
                      <>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 md:p-8 text-center">
                          <Upload className="mx-auto h-8 w-8 md:h-12 md:w-12 text-gray-400" />
                          <p className="mt-2 text-sm text-gray-600">
                            {selectedFile ? selectedFile.name : "Drag and drop or click to upload an image"}
                          </p>
                          <input 
                            type="file" 
                            className="hidden" 
                            accept="image/*"
                            ref={fileInputRef}
                            onChange={handleFileChange} 
                          />
                          <Button 
                            className="mt-4 bg-montessori-terracotta"
                            onClick={() => fileInputRef.current?.click()}
                          >
                            Select File
                          </Button>
                        </div>
                        <Button 
                          className="w-full bg-montessori-terracotta" 
                          onClick={handleUploadPhoto}
                          disabled={isUploading || !selectedFile || !photoTitle || !photoCategory}
                        >
                          {isUploading ? "Uploading..." : "Upload Photo"}
                        </Button>
                      </>
                    )}
                    {isEditingPhoto && (
                      <div className="flex gap-2">
                        <Button 
                          className="flex-1 bg-montessori-terracotta" 
                          onClick={handleEditPhoto}
                          disabled={!photoTitle || !photoCategory}
                        >
                          Save Changes
                        </Button>
                        <Button 
                          className="flex-1" 
                          variant="outline"
                          onClick={cancelEditPhoto}
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Gallery Photos ({photos.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingGallery ? (
                    <div className="text-center py-10">Loading gallery photos...</div>
                  ) : photos.length === 0 ? (
                    <div className="text-center py-10">No photos found. Upload your first photo!</div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                      {photos.map(photo => (
                        <Card key={photo.id}>
                          <img src={photo.image_url} alt={photo.title} className="w-full h-48 object-cover rounded-t-lg" />
                          <CardContent className="p-4">
                            <h3 className="font-medium">{photo.title}</h3>
                            <p className="text-sm text-gray-600 mb-2">{photo.category}</p>
                            <p className="text-sm text-gray-500 line-clamp-2">{photo.description}</p>
                            <div className="flex mt-4 space-x-2">
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="flex-1 flex items-center justify-center"
                                onClick={() => startEditPhoto(photo)}
                              >
                                <Edit className="h-4 w-4 mr-1" /> Edit
                              </Button>
                              <Button 
                                variant="destructive" 
                                size="sm" 
                                className="flex-1 flex items-center justify-center"
                                onClick={() => confirmDeleteItem('photo', photo.id)}
                              >
                                <Trash className="h-4 w-4 mr-1" /> Delete
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="messages" className="space-y-6">
              <Card className="bg-white shadow-md">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Contact Messages</span>
                    {notificationCounts.messages > 0 && (
                      <Badge variant="destructive">{notificationCounts.messages} Unread</Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingMessages ? (
                    <div className="text-center py-10">Loading messages...</div>
                  ) : messages.length === 0 ? (
                    <div className="text-center py-10 text-gray-500">No messages found.</div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map(msg => (
                        <Card 
                          key={msg.id} 
                          className={`transition-all duration-200 hover:shadow-lg ${
                            msg.read ? "bg-white" : "bg-montessori-beige/5 border-l-4 border-montessori-terracotta"
                          }`}
                        >
                          <CardContent className="p-3 md:p-4">
                            <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                              <div>
                                <h3 className="font-medium">{msg.name}</h3>
                                <p className="text-sm text-gray-600">{msg.email}</p>
                                {msg.phone && <p className="text-sm text-gray-600">{msg.phone}</p>}
                              </div>
                              <p className="text-sm text-gray-500 mt-1 md:mt-0">
                                {new Date(msg.created_at).toLocaleDateString()}
                              </p>
                            </div>
                            <p className="mt-2 font-semibold">{msg.subject}</p>
                            <p className="mt-2">{msg.message}</p>
                            <div className="flex mt-4 space-x-2">
                              {!msg.read && (
                                <Button 
                                  size="sm" 
                                  className="bg-montessori-terracotta"
                                  onClick={() => handleMarkAsRead(msg.id)}
                                >
                                  Mark as Read
                                </Button>
                              )}
                              <Button 
                                variant="destructive" 
                                size="sm"
                                onClick={() => confirmDeleteItem('message', msg.id)}
                              >
                                Delete
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="schedule" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Tour Schedule Requests</CardTitle>
                </CardHeader>
                <CardContent className="overflow-auto">
                  {isLoadingSchedules ? (
                    <div className="text-center py-10">Loading tour requests...</div>
                  ) : scheduleRequests.length === 0 ? (
                    <div className="text-center py-10">No tour requests found.</div>
                  ) : (
                    <div className="min-w-full overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead className="hidden md:table-cell">Contact Info</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead className="hidden md:table-cell">Message</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {scheduleRequests.map((request) => (
                            <TableRow key={request.id}>
                              <TableCell className="font-medium">{request.name}</TableCell>
                              <TableCell className="hidden md:table-cell">
                                <div>{request.email}</div>
                                <div className="text-sm text-gray-500">{request.phone}</div>
                              </TableCell>
                              <TableCell>{new Date(request.preferred_date).toLocaleDateString()}</TableCell>
                              <TableCell className="hidden md:table-cell max-w-[200px] truncate">
                                {request.message || "N/A"}
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-sm ${
                                  request.status === "pending" 
                                    ? "bg-yellow-100 text-yellow-800" 
                                    : request.status === "confirmed"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-red-100 text-red-800"
                                }`}>
                                  {request.status}
                                </span>
                              </TableCell>
                              <TableCell>
                                <div className="flex flex-col sm:flex-row gap-2">
                                  {request.status !== "confirmed" && (
                                    <Button 
                                      size="sm" 
                                      variant="outline"
                                      className="text-xs"
                                      onClick={() => handleUpdateScheduleStatus(request.id, 'confirmed')}
                                    >
                                      Confirm
                                    </Button>
                                  )}
                                  {request.status !== "declined" && (
                                    <Button 
                                      size="sm" 
                                      variant="destructive"
                                      className="text-xs"
                                      onClick={() => handleUpdateScheduleStatus(request.id, 'declined')}
                                    >
                                      Decline
                                    </Button>
                                  )}
                                  <Button
                                    size="sm"
                                    variant="destructive"
                                    className="text-xs"
                                    onClick={() => confirmDeleteItem('schedule', request.id)}
                                  >
                                    <Trash className="h-3 w-3" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="enrollment" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Enrollment Applications</CardTitle>
                </CardHeader>
                <CardContent className="overflow-auto">
                  {isLoadingEnrollments ? (
                    <div className="text-center py-10">Loading enrollment forms...</div>
                  ) : enrollmentForms.length === 0 ? (
                    <div className="text-center py-10">No enrollment forms found.</div>
                  ) : (
                    <div className="min-w-full overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Child</TableHead>
                            <TableHead className="hidden md:table-cell">Parent</TableHead>
                            <TableHead className="hidden sm:table-cell">Program</TableHead>
                            <TableHead className="hidden lg:table-cell">Info</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {enrollmentForms.map((form) => (
                            <TableRow key={form.id}>
                              <TableCell>
                                {form.child_name}
                                <div className="text-xs text-gray-500">Age: {form.child_age}</div>
                              </TableCell>
                              <TableCell className="hidden md:table-cell">
                                <div>{form.parent_name}</div>
                                <div className="text-sm text-gray-500">{form.email}</div>
                                <div className="text-sm text-gray-500">{form.phone}</div>
                              </TableCell>
                              <TableCell className="hidden sm:table-cell">{form.program}</TableCell>
                              <TableCell className="hidden lg:table-cell max-w-[200px] truncate">
                                {form.additional_info || "N/A"}
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-sm ${
                                  form.status === "pending" 
                                    ? "bg-yellow-100 text-yellow-800" 
                                    : form.status === "reviewed"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-red-100 text-red-800"
                                }`}>
                                  {form.status}
                                </span>
                              </TableCell>
                              <TableCell>
                                <div className="flex flex-col sm:flex-row gap-2">
                                  {form.status !== "reviewed" && (
                                    <Button 
                                      size="sm" 
                                      variant="outline"
                                      className="text-xs"
                                      onClick={() => handleUpdateEnrollmentStatus(form.id, 'reviewed')}
                                    >
                                      Review
                                    </Button>
                                  )}
                                  {form.status !== "rejected" && (
                                    <Button 
                                      size="sm" 
                                      variant="destructive"
                                      className="text-xs"
                                      onClick={() => handleUpdateEnrollmentStatus(form.id, 'rejected')}
                                    >
                                      Reject
                                    </Button>
                                  )}
                                  <Button
                                    size="sm"
                                    variant="destructive"
                                    className="text-xs"
                                    onClick={() => confirmDeleteItem('enrollment', form.id)}
                                  >
                                    <Trash className="h-3 w-3" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="blog" className="space-y-6">
              <BlogManager />
            </TabsContent>
            
            <TabsContent value="announcements" className="space-y-6">
              <AnnouncementManager />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      
      <Dialog open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
          </DialogHeader>
          <p>Are you sure you want to delete this item? This action cannot be undone.</p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteConfirmation(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteConfirmed}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminPanel;
