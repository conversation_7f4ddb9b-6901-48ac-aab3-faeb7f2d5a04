
import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BlogPost } from "@/types/firebase";
import { collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, orderBy, serverTimestamp } from "firebase/firestore";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { db, storage } from "@/lib/firebase";
import { toast } from "sonner";
import { Edit, Trash, Upload } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

const BlogManager = () => {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [excerpt, setExcerpt] = useState("");
  const [category, setCategory] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [editingPostId, setEditingPostId] = useState<string | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { data: posts, refetch } = useQuery({
    queryKey: ["admin-blog-posts"],
    queryFn: async () => {
      const q = query(collection(db, "blog_posts"), orderBy("created_at", "desc"));
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BlogPost));
    }
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      
      // Generate preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return null;

    try {
      setIsUploading(true);
      const fileExt = selectedFile.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `blog/${fileName}`;

      const storageRef = ref(storage, filePath);
      const uploadResult = await uploadBytes(storageRef, selectedFile);
      const downloadURL = await getDownloadURL(uploadResult.ref);

      setImageUrl(downloadURL);
      return downloadURL;
    } catch (error: any) {
      toast.error(`Upload failed: ${error.message}`);
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const slug = title.toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");

    try {
      let finalImageUrl = imageUrl;

      if (selectedFile) {
        const uploadedUrl = await handleFileUpload();
        if (uploadedUrl) finalImageUrl = uploadedUrl;
      }

      if (editingPostId) {
        const docRef = doc(db, "blog_posts", editingPostId);
        await updateDoc(docRef, {
          title,
          slug,
          content,
          excerpt,
          category,
          image_url: finalImageUrl,
          updated_at: serverTimestamp()
        });
        toast.success("Blog post updated successfully");
      } else {
        await addDoc(collection(db, "blog_posts"), {
          title,
          slug,
          content,
          excerpt,
          category,
          image_url: finalImageUrl,
          published: true,
          created_at: serverTimestamp(),
          updated_at: serverTimestamp()
        });
        toast.success("Blog post created successfully");
      }

      setTitle("");
      setContent("");
      setExcerpt("");
      setCategory("");
      setImageUrl("");
      setSelectedFile(null);
      setImagePreview(null);
      setEditingPostId(null);
      if (fileInputRef.current) fileInputRef.current.value = '';
      refetch();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleEdit = (post: BlogPost) => {
    setEditingPostId(post.id);
    setTitle(post.title);
    setContent(post.content);
    setExcerpt(post.excerpt);
    setCategory(post.category);
    setImageUrl(post.image_url || "");
    setImagePreview(post.image_url || null);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteDoc(doc(db, "blog_posts", id));
      toast.success("Blog post deleted successfully");
      refetch();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{editingPostId ? "Edit Blog Post" : "Create New Blog Post"}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                required
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="excerpt">Excerpt</Label>
              <Textarea
                id="excerpt"
                required
                value={excerpt}
                onChange={(e) => setExcerpt(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                required
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="min-h-[200px]"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Montessori Method">Montessori Method</SelectItem>
                    <SelectItem value="Child Development">Child Development</SelectItem>
                    <SelectItem value="Parenting Tips">Parenting Tips</SelectItem>
                    <SelectItem value="Educational Activities">Educational Activities</SelectItem>
                    <SelectItem value="School Updates">School Updates</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Featured Image</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                  <input
                    id="file-upload"
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                  
                  {imagePreview ? (
                    <div className="space-y-2">
                      <div className="relative w-full h-40 mx-auto">
                        <img 
                          src={imagePreview} 
                          alt="Preview" 
                          className="h-full max-h-40 mx-auto object-contain rounded"
                        />
                      </div>
                      <Button 
                        type="button" 
                        variant="outline" 
                        className="text-sm"
                        onClick={() => {
                          setSelectedFile(null);
                          setImagePreview(null);
                          setImageUrl("");
                          if (fileInputRef.current) fileInputRef.current.value = '';
                        }}
                      >
                        Remove Image
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="mx-auto h-8 w-8 text-gray-400" />
                      <p className="text-sm text-gray-500">Upload an image for your blog post</p>
                      <Button 
                        type="button" 
                        onClick={() => fileInputRef.current?.click()}
                        className="mt-2"
                      >
                        Select Image
                      </Button>
                    </div>
                  )}
                </div>
                
                {!imagePreview && (
                  <div className="mt-2">
                    <Label htmlFor="imageUrl" className="text-xs text-gray-500">Or use an image URL</Label>
                    <Input
                      id="imageUrl"
                      type="url"
                      value={imageUrl}
                      onChange={(e) => setImageUrl(e.target.value)}
                      placeholder="https://example.com/image.jpg"
                      className="mt-1"
                    />
                  </div>
                )}
              </div>
            </div>

            <Button type="submit" className="w-full bg-montessori-terracotta" disabled={isUploading}>
              {isUploading ? "Uploading..." : editingPostId ? "Update Post" : "Create Post"}
            </Button>

            {editingPostId && (
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={() => {
                  setEditingPostId(null);
                  setTitle("");
                  setContent("");
                  setExcerpt("");
                  setCategory("");
                  setImageUrl("");
                  setSelectedFile(null);
                  setImagePreview(null);
                  if (fileInputRef.current) fileInputRef.current.value = '';
                }}
              >
                Cancel Editing
              </Button>
            )}
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Blog Posts</CardTitle>
        </CardHeader>
        <CardContent>
          {!posts?.length ? (
            <div className="text-center py-10 text-gray-500">No blog posts found.</div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {posts.map((post) => (
                    <TableRow key={post.id}>
                      <TableCell className="font-medium">{post.title}</TableCell>
                      <TableCell>{post.category}</TableCell>
                      <TableCell>{new Date(post.created_at).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(post)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDelete(post.id)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default BlogManager;
