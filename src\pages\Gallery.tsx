
import { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import ScrollReveal from "@/components/animations/ScrollReveal";
import GalleryModal from "@/components/gallery/GalleryModal";
import GalleryFilters from "@/components/gallery/GalleryFilters";
import { getGalleryPhotos } from "@/utils/firebaseUtils";
import { GalleryPhoto } from "@/types/firebase";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<string>("all");
  const [photos, setPhotos] = useState<GalleryPhoto[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [imageLoaded, setImageLoaded] = useState<Record<string, boolean>>({});

  useEffect(() => {
    loadGalleryPhotos();
  }, []);

  const loadGalleryPhotos = async () => {
    try {
      const data = await getGalleryPhotos();
      setPhotos(data);
    } catch (error: any) {
      toast.error("Failed to load gallery photos");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const filteredImages = activeFilter === "all" 
    ? photos 
    : photos.filter(item => item.category.toLowerCase() === activeFilter);

  const handleImageLoad = (id: string) => {
    setImageLoaded(prev => ({
      ...prev,
      [id]: true
    }));
  };

  // Create a loading skeleton array
  const renderSkeletons = () => {
    return Array(6).fill(0).map((_, index) => (
      <ScrollReveal key={`skeleton-${index}`} delay={0.1} direction="up">
        <div className="relative overflow-hidden rounded-lg shadow-md h-80">
          <Skeleton className="w-full h-full" />
        </div>
      </ScrollReveal>
    ));
  };

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative py-16 md:py-24 bg-gradient-to-b from-montessori-beige/40 to-white">
        <div className="container mx-auto px-4">
          <ScrollReveal direction="up" delay={0.2}>
            <h1 className="font-serif text-4xl md:text-5xl lg:text-6xl font-bold text-center text-montessori-text mb-6">
              Our <span className="text-montessori-navy">Gallery</span>
            </h1>
            <p className="text-montessori-text/80 text-lg md:text-xl text-center max-w-3xl mx-auto mb-8">
              Glimpses into the vibrant life of our nursery and preschool community, showcasing children's work,
              classroom environments, and special events.
            </p>
          </ScrollReveal>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-8 md:py-16 bg-white mb-4 md:mb-0">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <GalleryFilters activeFilter={activeFilter} setActiveFilter={setActiveFilter} />
          </ScrollReveal>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
            {isLoading ? (
              renderSkeletons()
            ) : filteredImages.length === 0 ? (
              <p className="col-span-full text-center py-10">No images found for this category</p>
            ) : filteredImages.map((item) => (
              <ScrollReveal key={item.id} delay={0.1} direction="up">
                <div 
                  className="relative overflow-hidden rounded-lg shadow-md cursor-pointer group h-80"
                  onClick={() => setSelectedImage(item.id)}
                >
                  {!imageLoaded[item.id] && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                      <Skeleton className="w-full h-full absolute inset-0" />
                    </div>
                  )}
                  <img 
                    src={item.image_url} 
                    alt={item.title} 
                    className={`w-full h-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-110 ${imageLoaded[item.id] ? 'opacity-100' : 'opacity-0'}`}
                    onLoad={() => handleImageLoad(item.id)}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-6">
                    <h3 className="text-white font-serif text-xl font-bold">{item.title}</h3>
                    <p className="text-white/80 text-sm mt-2">{item.description}</p>
                  </div>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Modal for viewing images */}
      {selectedImage && photos.length > 0 && (
        <GalleryModal 
          isOpen={selectedImage !== null}
          onClose={() => setSelectedImage(null)}
          images={photos}
          currentImageId={selectedImage}
          setCurrentImageId={setSelectedImage}
        />
      )}
    </MainLayout>
  );
};

export default Gallery;
