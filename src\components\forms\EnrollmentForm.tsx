
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { submitEnrollmentForm } from "@/utils/firebaseUtils";

const EnrollmentForm = () => {
  const [parentName, setParentName] = useState("");
  const [childName, setChildName] = useState("");
  const [childAge, setChildAge] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [program, setProgram] = useState("");
  const [additionalInfo, setAdditionalInfo] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      await submitEnrollmentForm({
        parent_name: parentName,
        child_name: childName,
        child_age: childAge,
        email,
        phone,
        program,
        additional_info: additionalInfo
      });

      toast.success("Enrollment form submitted successfully!");
      
      // Reset form
      setParentName("");
      setChildName("");
      setChildAge("");
      setEmail("");
      setPhone("");
      setProgram("");
      setAdditionalInfo("");
    } catch (error: any) {
      toast.error(error.message || "Failed to submit enrollment form");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="parentName">Parent/Guardian Name</Label>
          <Input
            id="parentName"
            value={parentName}
            onChange={(e) => setParentName(e.target.value)}
            placeholder="Enter parent/guardian full name"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="childName">Child's Name</Label>
          <Input
            id="childName"
            value={childName}
            onChange={(e) => setChildName(e.target.value)}
            placeholder="Enter child's full name"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="childAge">Child's Age</Label>
          <Input
            id="childAge"
            value={childAge}
            onChange={(e) => setChildAge(e.target.value)}
            placeholder="Enter child's age"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email address"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            placeholder="Enter your phone number"
            required
          />
        </div>
        
        <div>
          <Label htmlFor="program">Program Interest</Label>
          <Select value={program} onValueChange={setProgram} required>
            <SelectTrigger id="program">
              <SelectValue placeholder="Select a program" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="nursery">Nursery (2-3 years)</SelectItem>
              <SelectItem value="preschool">Preschool (4-6 years)</SelectItem>
              <SelectItem value="aftercare">After School Care</SelectItem>
              <SelectItem value="holiday">Holiday Programs</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="additionalInfo">Additional Information (Optional)</Label>
          <Textarea
            id="additionalInfo"
            value={additionalInfo}
            onChange={(e) => setAdditionalInfo(e.target.value)}
            placeholder="Any additional information about your child or questions..."
            className="resize-none"
            rows={4}
          />
        </div>
      </div>
      
      <Button 
        type="submit" 
        className="w-full bg-montessori-terracotta hover:bg-montessori-terracotta/90 text-white"
        disabled={isSubmitting}
      >
        {isSubmitting ? "Submitting..." : "Submit Enrollment Request"}
      </Button>
    </form>
  );
};

export default EnrollmentForm;
