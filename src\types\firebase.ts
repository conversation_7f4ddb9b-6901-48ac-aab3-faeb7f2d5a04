import { Timestamp } from 'firebase/firestore';

export interface GalleryPhoto {
  id: string;
  title: string;
  description: string;
  category: string;
  image_url: string;
  created_at: Timestamp;
}

export interface ContactMessage {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  read: boolean;
  created_at: Timestamp;
}

export interface TourSchedule {
  id: string;
  name: string;
  email: string;
  phone: string;
  preferred_date: string;
  message?: string;
  status: 'pending' | 'confirmed' | 'declined';
  created_at: Timestamp;
}

export interface EnrollmentForm {
  id: string;
  parent_name: string;
  child_name: string;
  child_age: string;
  email: string;
  phone: string;
  program: string;
  additional_info?: string;
  status: 'pending' | 'reviewed' | 'rejected';
  created_at: Timestamp;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  category: string;
  image_url?: string;
  published: boolean;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface Announcement {
  id: string;
  title: string;
  date: string;
  description: string;
  category: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}
