
import MainLayout from "@/components/layout/MainLayout";
import { motion } from "framer-motion";
import ScrollReveal from "@/components/animations/ScrollReveal";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Users, BookOpen, Award, Heart, Globe } from "lucide-react";

const About = () => {
  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-to-b from-montessori-beige/40 to-white">
        <div className="container mx-auto px-4">
          <ScrollReveal direction="up" delay={0.2}>
            <h1 className="font-serif text-4xl md:text-5xl lg:text-6xl font-bold text-center text-montessori-text mb-6">
              About <span className="text-montessori-terracotta">Given Promise Academy</span>
            </h1>
            <p className="text-montessori-text/80 text-lg md:text-xl text-center max-w-3xl mx-auto mb-12">
              For over a decade, Given Promise Academy has been providing quality early childhood education,
              nurturing young minds through play-based learning and preparing children for their educational journey.
            </p>
          </ScrollReveal>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <ScrollReveal direction="left">
              <div className="relative">
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-montessori-sage/30 rounded-full"></div>
                <img
                  src="https://images.unsplash.com/photo-1587654780291-39c9404d746b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Children playing in nursery classroom"
                  className="rounded-lg shadow-xl relative z-10"
                />
                <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-montessori-terracotta/20 rounded-full"></div>
              </div>
            </ScrollReveal>

            <ScrollReveal direction="right" delay={0.3}>
              <h2 className="font-serif text-3xl font-bold text-montessori-text mb-6">
                Our Story
              </h2>
              <p className="text-montessori-text/80 mb-4">
                Given Promise Academy was founded in 2010 by a team of dedicated early childhood educators who
                believed in providing quality nursery and preschool education. What started as a small nursery
                with 20 children has grown into a trusted institution serving over 150 families in our community.
              </p>
              <p className="text-montessori-text/80 mb-8">
                Our academy has expanded while maintaining our core values: creating a safe, nurturing environment
                where children can learn through play, develop social skills, and build confidence for their
                educational journey ahead.
              </p>
              <div className="flex flex-wrap gap-8">
                <div>
                  <p className="text-4xl font-bold text-montessori-terracotta">14+</p>
                  <p className="text-montessori-text/70">Years of Excellence</p>
                </div>
                <div>
                  <p className="text-4xl font-bold text-montessori-terracotta">150+</p>
                  <p className="text-montessori-text/70">Families Served</p>
                </div>
                <div>
                  <p className="text-4xl font-bold text-montessori-terracotta">12+</p>
                  <p className="text-montessori-text/70">Qualified Teachers</p>
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="py-20 bg-montessori-beige/30">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="font-serif text-3xl font-bold text-center text-montessori-text mb-16">
              Our Core Values
            </h2>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Heart,
                title: "Nurturing Care",
                desc: "We provide a warm, loving environment where every child feels safe and valued."
              },
              {
                icon: BookOpen,
                title: "Early Learning",
                desc: "We introduce foundational skills through play-based activities that make learning fun."
              },
              {
                icon: Users,
                title: "Family Partnership",
                desc: "We work closely with families to support each child's development and growth."
              },
              {
                icon: Award,
                title: "Quality Education",
                desc: "We maintain high standards in our programs and provide qualified, caring teachers."
              },
              {
                icon: Globe,
                title: "Cultural Awareness",
                desc: "We celebrate diversity and help children appreciate different cultures and traditions."
              },
              {
                icon: Heart,
                title: "Character Building",
                desc: "We instill values of kindness, respect, and responsibility in young children."
              }
            ].map((value, index) => (
              <ScrollReveal key={index} direction={index % 2 === 0 ? "up" : "down"} delay={index * 0.1}>
                <div className="bg-white p-8 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 h-full">
                  <div className="bg-montessori-sage/30 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                    <value.icon className="h-8 w-8 text-montessori-terracotta" />
                  </div>
                  <h3 className="font-serif text-xl font-semibold mb-3 text-montessori-text">{value.title}</h3>
                  <p className="text-montessori-text/80">{value.desc}</p>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* Our Team Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <h2 className="font-serif text-3xl font-bold text-center text-montessori-text mb-6">
              Our Leadership Team
            </h2>
            <p className="text-montessori-text/80 text-center max-w-3xl mx-auto mb-16">
              Meet the passionate educators and administrators who make Given Promise Academy a special place for children to learn and grow.
            </p>
          </ScrollReveal>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "Grace Mwalimu",
                role: "Academy Director",
                image: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-1.2.1&auto=format&fit=crop&q=80&w=800",
                bio: "With over 15 years in early childhood education, Grace leads our academy with passion and dedication to nurturing young minds."
              },
              {
                name: "James Kiprotich",
                role: "Head of Curriculum",
                image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&q=80&w=800",
                bio: "James ensures our programs are developmentally appropriate and engaging for children aged 2-6 years."
              },
              {
                name: "Mary Wanjiku",
                role: "Nursery Program Lead",
                image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&q=80&w=800",
                bio: "Mary's caring approach helps our youngest learners feel secure and excited about their first school experience."
              },
            ].map((person, index) => (
              <ScrollReveal key={index} direction="up" delay={index * 0.2}>
                <div className="bg-montessori-beige/20 rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <img 
                    src={person.image} 
                    alt={person.name} 
                    className="w-full h-64 object-cover object-center"
                  />
                  <div className="p-6">
                    <h3 className="font-serif text-xl font-semibold mb-1 text-montessori-text">{person.name}</h3>
                    <p className="text-montessori-terracotta font-medium mb-3">{person.role}</p>
                    <p className="text-montessori-text/80">{person.bio}</p>
                  </div>
                </div>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-montessori-sage/20">
        <div className="container mx-auto px-4">
          <ScrollReveal>
            <div className="bg-white rounded-xl shadow-lg p-8 md:p-12 text-center max-w-4xl mx-auto">
              <h2 className="font-serif text-3xl font-bold mb-6 text-montessori-text">
                Join the Given Promise Family
              </h2>
              <p className="text-lg text-montessori-text/80 mb-8 max-w-2xl mx-auto">
                Visit us to see our nurturing environment and discover how we can support your child's early learning journey.
              </p>
              <Button className="bg-montessori-terracotta hover:bg-montessori-terracotta/90 text-white px-6 py-6 text-lg">
                Schedule a Tour <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </ScrollReveal>
        </div>
      </section>
    </MainLayout>
  );
};

export default About;
