
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Facebook, Instagram, Twitter, Mail, MapPin, Phone, History, Cookie } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const navigate = useNavigate();

  const handleNavigation = (path: string) => {
    navigate(path);
    window.scrollTo(0, 0);
  };
  
  return (
    <footer className="bg-montessori-beige py-12 border-t border-montessori-sage/30">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* School Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center md:text-left"
          >
            <h3 className="text-xl font-serif font-semibold mb-4 text-montessori-text">
              Given Promise Academy
            </h3>
            <p className="text-montessori-text/80 mb-4">
              A nurturing nursery and preschool where young children develop essential skills through play-based learning and caring guidance.
            </p>
            <div className="flex justify-center md:justify-start space-x-4">
              <a href="https://facebook.com/givenpromiseacademy" target="_blank" rel="noopener noreferrer" className="text-montessori-text/80 hover:text-montessori-terracotta transition-colors">
                <Facebook size={20} />
              </a>
              <a href="https://instagram.com/givenpromiseacademy" target="_blank" rel="noopener noreferrer" className="text-montessori-text/80 hover:text-montessori-terracotta transition-colors">
                <Instagram size={20} />
              </a>
              <a href="https://twitter.com/givenpromiseacademy" target="_blank" rel="noopener noreferrer" className="text-montessori-text/80 hover:text-montessori-terracotta transition-colors">
                <Twitter size={20} />
              </a>
              <a href="mailto:<EMAIL>" className="text-montessori-text/80 hover:text-montessori-terracotta transition-colors">
                <Mail size={20} />
              </a>
            </div>
          </motion.div>
          
          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-center md:text-left"
          >
            <h3 className="text-xl font-serif font-semibold mb-4 text-montessori-text">
              Quick Links
            </h3>
            <ul className="space-y-2">
              {[
                "Home",
                "About",
                "Programs",
                "Gallery",
                "Blog",
                "Contact",
                "Enroll Now",
                "Announcements",
                "Cookie Settings"
              ].map((item) => (
                <li key={item} className="flex justify-center md:justify-start">
                  <button
                    onClick={() => handleNavigation(
                      item === "Home" 
                        ? "/" 
                        : `/${item.toLowerCase().replace(" ", "-")}`
                    )}
                    className="text-montessori-text/80 hover:text-montessori-terracotta transition-colors"
                  >
                    {item}
                  </button>
                </li>
              ))}
            </ul>
          </motion.div>
          
          {/* Programs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center md:text-left"
          >
            <h3 className="text-xl font-serif font-semibold mb-4 text-montessori-text">
              Our Programs
            </h3>
            <ul className="space-y-2">
              {[
                "Nursery (2-3 years)",
                "Preschool (4-6 years)",
                "After School Care",
                "Holiday Programs",

              ].map((item) => (
                <li key={item} className="flex justify-center md:justify-start">
                  <button
                    onClick={() => handleNavigation("/programs")}
                    className="text-montessori-text/80 hover:text-montessori-terracotta transition-colors"
                  >
                    {item}
                  </button>
                </li>
              ))}
            </ul>
          </motion.div>
          
          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
            className="text-center md:text-left"
          >
            <h3 className="text-xl font-serif font-semibold mb-4 text-montessori-text">
              Contact Us
            </h3>
            <ul className="space-y-4">
              <li className="flex items-start justify-center md:justify-start space-x-3">
                <MapPin size={20} className="text-montessori-terracotta flex-shrink-0 mt-1" />
                <span className="text-montessori-text/80">
                  P.O. Box 1234, Dar es Salaam, Tanzania
                </span>
              </li>
              <li className="flex items-center justify-center md:justify-start space-x-3">
                <Phone size={20} className="text-montessori-terracotta" />
                <span className="text-montessori-text/80">+255 754 123 456</span>
              </li>
              <li className="flex items-center justify-center md:justify-start space-x-3">
                <Mail size={20} className="text-montessori-terracotta" />
                <span className="text-montessori-text/80"><EMAIL></span>
              </li>
            </ul>
          </motion.div>
        </div>
        
        <div className="border-t border-montessori-sage/30 mt-10 pt-6 flex flex-col items-center space-y-4">
          <div className="flex flex-wrap justify-center gap-4 text-sm text-montessori-text/70">
            <button 
              onClick={() => handleNavigation('/admin')}
              className="hover:text-montessori-terracotta transition-colors"
            >
              Admin Panel
            </button>
            <span>•</span>
            <button
              onClick={() => handleNavigation('/history')}
              className="flex items-center space-x-1 hover:text-montessori-terracotta transition-colors"
            >
              <History size={16} />
              <span>Our History</span>
            </button>
            <span>•</span>
            <button
              onClick={() => handleNavigation('/cookie-settings')}
              className="flex items-center space-x-1 hover:text-montessori-terracotta transition-colors"
            >
              <Cookie size={16} />
              <span>Cookie Settings</span>
            </button>
          </div>
          <p className="text-montessori-text/70 text-center">&copy; {currentYear} Given Promise Academy. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
