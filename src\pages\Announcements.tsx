
import { useQuery } from "@tanstack/react-query";
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { getAnnouncements } from "@/utils/firebaseUtils";
import { Announcement } from "@/types/firebase";
import { useIsMobile } from "@/hooks/use-mobile";

const Announcements = () => {
  const { data: announcements, isLoading } = useQuery({
    queryKey: ["announcements"],
    queryFn: getAnnouncements
  });
  
  const isMobile = useIsMobile();

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-4xl font-serif font-semibold text-montessori-text mb-8 text-center">
          School Announcements
        </h1>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="animate-pulse text-center">
              <p className="text-montessori-text/80">Loading announcements...</p>
            </div>
          </div>
        ) : announcements?.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-montessori-text/80">No announcements available at the moment.</p>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {announcements?.map((announcement: Announcement) => (
              <Card key={announcement.id} className="transform transition-transform hover:-translate-y-1 bg-white border-t-4 border-montessori-navy flex flex-col h-full overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start flex-wrap gap-2">
                    <CardTitle className="text-xl font-serif text-montessori-text line-clamp-2 break-words overflow-hidden max-w-full">
                      {announcement.title}
                    </CardTitle>
                    <span className="inline-flex items-center rounded-full bg-montessori-navy/10 px-2.5 py-0.5 text-xs font-medium text-montessori-navy shrink-0 whitespace-nowrap">
                      {announcement.category}
                    </span>
                  </div>
                  <CardDescription>
                    {new Date(announcement.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow pt-2 overflow-hidden">
                  <p className="text-montessori-text/80 break-words overflow-hidden line-clamp-4 sm:line-clamp-4 text-sm sm:text-base">
                    {announcement.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default Announcements;
