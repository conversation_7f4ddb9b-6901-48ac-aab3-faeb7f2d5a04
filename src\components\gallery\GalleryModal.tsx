
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, X } from "lucide-react";
import { GalleryPhoto } from "@/types/supabase";

interface GalleryModalProps {
  isOpen: boolean;
  onClose: () => void;
  images: GalleryPhoto[];
  currentImageId: string | null;
  setCurrentImageId: (id: string | null) => void;
}

const GalleryModal = ({ isOpen, onClose, images, currentImageId, setCurrentImageId }: GalleryModalProps) => {
  if (!currentImageId) return null;

  const currentImageIndex = images.findIndex(img => img.id === currentImageId);
  if (currentImageIndex === -1) return null;
  
  const currentImage = images[currentImageIndex];

  const handleNext = () => {
    const nextIndex = (currentImageIndex + 1) % images.length;
    setCurrentImageId(images[nextIndex].id);
  };

  const handlePrevious = () => {
    const prevIndex = (currentImageIndex - 1 + images.length) % images.length;
    setCurrentImageId(images[prevIndex].id);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case "ArrowRight":
        handleNext();
        break;
      case "ArrowLeft":
        handlePrevious();
        break;
      case "Escape":
        onClose();
        break;
      default:
        break;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        className="max-w-5xl w-[95vw] h-[90vh] p-0 bg-white overflow-hidden rounded-lg shadow-xl"
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        <div className="relative flex flex-col h-full overflow-hidden">
          {/* Close button */}
          <Button 
            variant="ghost" 
            className="absolute right-4 top-4 z-50 bg-white/90 text-montessori-text hover:bg-montessori-beige rounded-full p-2 h-auto w-auto shadow-md"
            onClick={onClose}
          >
            <X className="h-6 w-6" />
          </Button>

          {/* Image container - now with scrolling */}
          <div className="flex-grow flex items-center justify-center p-4 md:p-6 bg-white/95 overflow-y-auto">
            <div className="relative max-w-full max-h-full">
              <img
                src={currentImage.image_url}
                alt={currentImage.title}
                className="max-h-[70vh] max-w-full object-contain shadow-md"
              />
            </div>
          </div>

          {/* Navigation arrows */}
          <div className="absolute top-1/2 -translate-y-1/2 left-0 w-full flex justify-between px-4 z-40">
            <Button 
              variant="ghost" 
              className="bg-white/80 text-montessori-text hover:bg-white rounded-full p-2 h-auto w-auto shadow-md"
              onClick={handlePrevious}
            >
              <ArrowLeft className="h-6 w-6" />
            </Button>
            <Button 
              variant="ghost" 
              className="bg-white/80 text-montessori-text hover:bg-white rounded-full p-2 h-auto w-auto shadow-md"
              onClick={handleNext}
            >
              <ArrowRight className="h-6 w-6" />
            </Button>
          </div>

          {/* Image info - scrollable on small screens */}
          <div className="bg-white shadow-md p-6 overflow-y-auto">
            <h3 className="text-xl font-serif font-bold mb-2 text-montessori-text">{currentImage.title}</h3>
            <p className="text-montessori-text/70">{currentImage.description}</p>
            
            {/* Image counter */}
            <div className="mt-2 text-center">
              <span className="text-sm text-montessori-text/70">
                {currentImageIndex + 1} of {images.length}
              </span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GalleryModal;
